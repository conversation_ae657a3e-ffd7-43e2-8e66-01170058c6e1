# 交易参数构建重构总结

## 重构目标
对 `AbstractUpdateTradeParamsTemplate`、`FuyouUpdateTradeParams`、`HaikeUpdateTradeParams`、`LklV3UpdateTradeParams` 这4个类中的 `buildNewNeedInsertParamsAndReturnOldNewMapV3` 方法进行重构，减少重复代码，提高代码可维护性。

## 重构前的问题
1. **代码重复严重**：4个类中的 `buildNewNeedInsertParamsAndReturnOldNewMapV3` 方法有大量重复逻辑
2. **维护困难**：相同的业务逻辑分散在多个类中，修改时需要同步更新多处
3. **扩展性差**：新增收单机构或支付方式需要修改多个地方
4. **可读性差**：方法过长，业务逻辑混杂

## 重构方案
采用 **模板方法模式** + **策略模式** 的组合设计模式：

### 1. 模板方法模式
- 创建抽象基类 `AbstractTradeParamsBuilder`
- 定义构建交易参数的通用流程
- 将变化的部分抽象为抽象方法，由子类实现

### 2. 策略模式
- 创建支付方式处理器接口 `PaywayProcessor`
- 为不同的支付方式（收单机构、支付宝、微信、银联）创建具体处理器
- 使用工厂模式管理处理器的创建和获取

## 重构后的架构

### 核心类结构
```
AbstractTradeParamsBuilder (抽象基类)
├── LklV3TradeParamsBuilder (拉卡拉V3实现)
├── FuyouTradeParamsBuilder (富友实现)
└── HaikeTradeParamsBuilder (海科实现)

PaywayProcessor (策略接口)
├── AcquirerPaywayProcessor (收单机构处理器)
├── AlipayPaywayProcessor (支付宝处理器)
├── WeixinPaywayProcessor (微信处理器)
└── UnionpayPaywayProcessor (银联处理器)

PaywayProcessorFactory (工厂类)
TradeParamsBuilderContext (上下文类)
TradeParamsBuilderConfig (配置类)
```

### 文件清单
1. **抽象基类**
   - `AbstractTradeParamsBuilder.java` - 定义构建流程的模板方法

2. **上下文类**
   - `TradeParamsBuilderContext.java` - 封装构建过程中的数据

3. **策略接口**
   - `PaywayProcessor.java` - 支付方式处理器接口

4. **工厂类**
   - `PaywayProcessorFactory.java` - 处理器工厂

5. **具体处理器**
   - `AcquirerPaywayProcessor.java` - 收单机构处理器
   - `AlipayPaywayProcessor.java` - 支付宝处理器
   - `WeixinPaywayProcessor.java` - 微信处理器
   - `UnionpayPaywayProcessor.java` - 银联处理器

6. **具体构建器**
   - `LklV3TradeParamsBuilder.java` - 拉卡拉V3构建器
   - `FuyouTradeParamsBuilder.java` - 富友构建器
   - `HaikeTradeParamsBuilder.java` - 海科构建器

7. **配置类**
   - `TradeParamsBuilderConfig.java` - 处理器注册配置

8. **枚举增强**
   - 完善了 `PaywayEnum.java`，添加了缺失的枚举值

## 重构收益

### 1. 代码复用
- 将重复的业务逻辑提取到抽象基类中
- 4个类的方法从平均70行减少到3行

### 2. 可维护性提升
- 相同的业务逻辑集中管理
- 修改时只需要修改对应的处理器

### 3. 扩展性增强
- 新增收单机构：只需创建新的构建器类
- 新增支付方式：只需创建新的处理器类
- 符合开闭原则

### 4. 可读性改善
- 职责分离，每个类的职责更加明确
- 方法长度大幅缩短，逻辑更清晰

### 5. 设计模式应用
- 模板方法模式：定义算法骨架，延迟具体实现
- 策略模式：封装算法族，使其可以互相替换
- 工厂模式：统一管理对象创建

## 兼容性保证
- 保持原有方法签名不变
- 保持原有业务逻辑不变
- 原有调用方无需修改

## 使用方式
重构后的使用方式与之前完全相同：
```java
// 原有调用方式保持不变
Map<String, MerchantProviderParamsDO> result = 
    lklV3UpdateTradeParams.buildNewNeedInsertParamsAndReturnOldNewMapV3(mainTaskDO, subTaskDO);
```

## 总结
通过本次重构，成功地：
1. 消除了代码重复
2. 提高了代码的可维护性和扩展性
3. 应用了经典的设计模式
4. 保持了向后兼容性

这是一个典型的重构案例，展示了如何在不破坏现有功能的前提下，通过设计模式来改善代码结构。
