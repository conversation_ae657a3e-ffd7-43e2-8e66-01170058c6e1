# 交易参数构建器使用示例

## 概述
本文档展示了重构后的交易参数构建器的使用方法和扩展方式。

## 基本使用

### 1. 现有功能使用（无需修改）
```java
@Component
public class SomeService {
    
    @Autowired
    private LklV3UpdateTradeParams lklV3UpdateTradeParams;
    
    public void processTradeParams() {
        // 使用方式与重构前完全相同
        Map<String, MerchantProviderParamsDO> result = 
            lklV3UpdateTradeParams.buildNewNeedInsertParamsAndReturnOldNewMapV3(mainTaskDO, subTaskDO);
    }
}
```

### 2. 直接使用构建器
```java
@Component
public class SomeService {
    
    @Autowired
    private LklV3TradeParamsBuilder lklV3TradeParamsBuilder;
    
    public void processTradeParams() {
        // 也可以直接使用构建器
        Map<String, MerchantProviderParamsDO> result = 
            lklV3TradeParamsBuilder.buildNewNeedInsertParamsAndReturnOldNewMapV3(mainTaskDO, subTaskDO);
    }
}
```

## 扩展示例

### 1. 新增收单机构
假设要新增一个"新收单机构"，步骤如下：

#### 步骤1：创建构建器
```java
@Component
public class NewAcquirerTradeParamsBuilder extends AbstractTradeParamsBuilder {
    
    public static final String ACQUIRER_TYPE = "NEW_ACQUIRER";
    
    @Autowired
    private NewAcquirerFacade newAcquirerFacade;
    
    @Override
    protected String getAcquirerType() {
        return ACQUIRER_TYPE;
    }
    
    @Override
    protected Object getMerchantAcquireInfo(TradeParamsBuilderContext context) {
        return newAcquirerFacade.getAcquireInfoFromContractSubTask(context.getContractTaskId());
    }
}
```

#### 步骤2：在配置类中注册处理器
```java
@Configuration
public class TradeParamsBuilderConfig {
    
    @PostConstruct
    public void init() {
        // 现有注册...
        
        // 新增收单机构处理器注册
        registerNewAcquirerProcessors();
    }
    
    private void registerNewAcquirerProcessors() {
        String acquirerType = "NEW_ACQUIRER";
        PaywayProcessorFactory.registerProcessor(new AcquirerPaywayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createAlipayProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createWeixinProcessor(acquirerType));
        PaywayProcessorFactory.registerProcessor(createUnionpayProcessor(acquirerType));
    }
}
```

#### 步骤3：创建对应的UpdateTradeParams类
```java
@Component
public class NewAcquirerUpdateTradeParams extends AbstractUpdateTradeParamsTemplate {
    
    @Autowired
    private NewAcquirerTradeParamsBuilder newAcquirerTradeParamsBuilder;
    
    @Override
    Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMapV3(
            InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        return newAcquirerTradeParamsBuilder.buildNewNeedInsertParamsAndReturnOldNewMapV3(mainTaskDO, subTaskDO);
    }
    
    // 其他抽象方法实现...
}
```

### 2. 新增支付方式
假设要新增一个"数字货币"支付方式：

#### 步骤1：在PaywayEnum中添加枚举值
```java
public enum PaywayEnum implements ITextValueEnum {
    // 现有枚举...
    
    /**
     * 数字货币
     */
    DIGITAL_CURRENCY(24, "数字货币");
}
```

#### 步骤2：创建处理器
```java
public class DigitalCurrencyPaywayProcessor implements PaywayProcessor {
    
    private final String acquirerType;
    
    public DigitalCurrencyPaywayProcessor(String acquirerType) {
        this.acquirerType = acquirerType;
    }
    
    @Override
    public void processParam(MerchantProviderParamsDO newParam, 
                           MerchantProviderParamsDO oldParam, 
                           TradeParamsBuilderContext context) {
        // 数字货币特定的处理逻辑
        MerchantAcquireInfoBO merchantAcquireInfo = (MerchantAcquireInfoBO) context.getMerchantAcquireInfo();
        newParam.setPayMerchantId(merchantAcquireInfo.getDigitalCurrencyNo());
        // 其他特定逻辑...
    }
    
    @Override
    public Integer getSupportedPayway() {
        return PaywayEnum.DIGITAL_CURRENCY.getValue();
    }
    
    @Override
    public String getSupportedAcquirerType() {
        return acquirerType;
    }
}
```

#### 步骤3：在配置类中注册
```java
private void registerLklV3Processors() {
    String acquirerType = "LKLV3";
    // 现有注册...
    
    // 新增数字货币处理器
    PaywayProcessorFactory.registerProcessor(new DigitalCurrencyPaywayProcessor(acquirerType));
}
```

### 3. 自定义处理逻辑
如果某个收单机构对特定支付方式有特殊处理需求：

```java
public class CustomAlipayPaywayProcessor extends AlipayPaywayProcessor {
    
    public CustomAlipayPaywayProcessor(String acquirerType) {
        super(acquirerType);
    }
    
    @Override
    public void processParam(MerchantProviderParamsDO newParam, 
                           MerchantProviderParamsDO oldParam, 
                           TradeParamsBuilderContext context) {
        // 先执行基础逻辑
        super.processParam(newParam, oldParam, context);
        
        // 再执行自定义逻辑
        if ("SPECIAL_ACQUIRER".equals(getSupportedAcquirerType())) {
            // 特殊处理逻辑
            newParam.setExtra(customizeExtra(newParam.getExtra()));
        }
    }
    
    private String customizeExtra(String originalExtra) {
        // 自定义Extra处理逻辑
        return originalExtra;
    }
}
```

## 最佳实践

### 1. 处理器设计原则
- 单一职责：每个处理器只处理一种支付方式
- 无状态：处理器应该是无状态的，便于复用
- 可扩展：通过继承可以轻松扩展功能

### 2. 错误处理
```java
@Override
public void processParam(MerchantProviderParamsDO newParam, 
                       MerchantProviderParamsDO oldParam, 
                       TradeParamsBuilderContext context) {
    try {
        // 处理逻辑
    } catch (Exception e) {
        log.error("处理支付方式参数失败, payway: {}, acquirer: {}", 
                 getSupportedPayway(), getSupportedAcquirerType(), e);
        throw new ContractBizException("处理支付方式参数失败", e);
    }
}
```

### 3. 日志记录
```java
@Override
public void processParam(MerchantProviderParamsDO newParam, 
                       MerchantProviderParamsDO oldParam, 
                       TradeParamsBuilderContext context) {
    log.debug("开始处理支付方式参数, payway: {}, acquirer: {}", 
             getSupportedPayway(), getSupportedAcquirerType());
    
    // 处理逻辑...
    
    log.debug("完成处理支付方式参数, payway: {}, acquirer: {}", 
             getSupportedPayway(), getSupportedAcquirerType());
}
```

## 总结
重构后的架构提供了良好的扩展性和可维护性，新增功能时只需要：
1. 创建对应的构建器或处理器
2. 在配置类中注册
3. 无需修改现有代码

这种设计符合开闭原则，使得系统更加灵活和健壮。
