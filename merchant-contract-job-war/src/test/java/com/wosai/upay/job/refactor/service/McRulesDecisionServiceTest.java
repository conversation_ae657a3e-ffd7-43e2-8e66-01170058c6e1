package com.wosai.upay.job.refactor.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.shouqianba.cua.enums.contract.AcquirerTypeEnum;
import com.shouqianba.cua.utils.thread.CollectionWorker;
import com.wosai.assistant.response.UserBean;
import com.wosai.assistant.service.UserRpcService;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.biz.BusinessRuleBiz;
import com.wosai.upay.job.model.NetInRuleGroups;
import com.wosai.upay.job.refactor.mapper.MerchantProviderParamsDynamicMapper;
import com.wosai.upay.job.refactor.model.bo.ContractGroupRuleVerifyResultBO;
import com.wosai.upay.job.refactor.model.bo.MerchantFeatureBO;
import com.wosai.upay.job.refactor.model.entity.GroupCombinedStrategyDetailDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.model.enums.Deleted;
import com.wosai.upay.job.refactor.service.impl.McRulesDecisionServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * 规则决策测试类
 * 现有规则:
 *          1 宁波市不能入拉卡拉 (330200)
 *          2 推广组织00069不能进fuyou
 *          3 拉卡拉组织商户只能走lkl (999999)
 *          4 商户名称包含`南京莜面村餐饮管理有限公司`进ums
 *          5 "山西或者贵州, 主fuyou 辅lkl"  (140000, 520000)
 *          6 宁波市进fuyou (330200)
 *          7 兜底拉卡拉
 *
 * <AUTHOR>
 * @date 2023/11/29 16:59
 */
@Slf4j
public class McRulesDecisionServiceTest extends BaseTest {

    @Resource
    private McRulesDecisionServiceImpl mcRulesDecisionService;


    private final MerchantFeatureBO merchantFeatureBO = new MerchantFeatureBO();

    @Before
    public void initMerchant() {
        merchantFeatureBO.setMerchantSn("test001");
        MerchantFeatureBO.ExtraFeature extraFeature = merchantFeatureBO.new ExtraFeature();
        MerchantFeatureBO.ExtraFeature.AccountInfo accountInfo = extraFeature.new AccountInfo();
        accountInfo.setAccountType(1);
        accountInfo.setIdentityId("540127198108113435");
        accountInfo.setHolderName("齐天大圣");
        MerchantBusinessLicenseInfo merchantBusinessLicenseInfo = new MerchantBusinessLicenseInfo();
        merchantBusinessLicenseInfo.setLegal_person_id_number("540127198108113435");
        merchantBusinessLicenseInfo.setName("");
        merchantBusinessLicenseInfo.setType(1);
        extraFeature.setAccountInfo(accountInfo);
        extraFeature.setMerchantBusinessLicenseInfo(merchantBusinessLicenseInfo);
        merchantFeatureBO.setExtraFeature(extraFeature);
    }


    /**
     * 宁波市不能入拉卡拉 (330200),进fuyou
     */
    @Test
    public void testNestRule1() {
        merchantFeatureBO.setCityCode("330200");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO);
        for (GroupCombinedStrategyDetailDO detailDO : detailDOS) {
            assertThat(detailDO.getGroupId()).isNotEqualTo("lklorg");
            assertThat(detailDO.getGroupId()).isEqualTo("fuyou");
        }
    }

    /**
     * 推广组织00069(大客户)不能进fuyou
     */
    @Test
    public void testNestRule2() {
        merchantFeatureBO.setPromotionOrganizationPath("00069");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO);
        assertThat(detailDOS).isNotEmpty();
        for (GroupCombinedStrategyDetailDO detailDO : detailDOS) {
            assertThat(detailDO.getGroupId()).isNotEqualTo("fuyou");
        }
        log.info("details: {}", detailDOS);
    }

    /**
     *  商户名称包含`南京莜面村餐饮管理有限公司`进ums
     */
    @Test
    public void testNestRule3() {
        merchantFeatureBO.setName("南京莜面村餐饮管理有限公司啦啦啦");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO);
        assertThat(detailDOS).hasSize(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("ums");
    }

    /**
     * "山西或者贵州, 主fuyou 辅lkl"  (140000, 520000)
     */
    @Test
    public void testNestRule4() {
        merchantFeatureBO.setProvinceCode("140000");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO);
        assertThat(detailDOS).hasSize(2);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    /**
     * 宁波市进fuyou (330200)
     */
    @Test
    public void testNestRule5() {
        merchantFeatureBO.setCityCode("330200");
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO);
        assertThat(detailDOS).hasSize(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("fuyou");
    }

    /**
     * 兜底拉卡拉
     */
    @Test
    public void testNestRule6() {
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupByMerchantFeature(merchantFeatureBO);
        assertThat(detailDOS).hasSize(1);
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    @Autowired
    private BusinessRuleBiz businessRuleBiz;

    @Test
    public void testNestRule8() {
        NetInRuleGroups groups = businessRuleBiz.getRuleGroupId("21690003426396", null);
        assertThat(groups).isNotNull();
    }

    @Test
    public void testNestRule7() {
        String merchantSn = "mch-1680001206566";
        Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupBySnAndOrg(merchantSn, null);
        assertThat(detailDOS).isNotEmpty();
        Iterator<GroupCombinedStrategyDetailDO> iterator = detailDOS.iterator();
        assertThat(iterator.next().getGroupId()).isEqualTo("lklorg");
    }

    @Resource
    private MerchantProviderParamsDynamicMapper paramsDynamicMapper;

    @Test
    public void multiTest() {
        LambdaQueryWrapper<MerchantProviderParamsDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper
                .select(MerchantProviderParamsDO::getMerchantSn)
                .eq(MerchantProviderParamsDO::getDeleted, Deleted.NO_DELETED.getValue())
                .ge(MerchantProviderParamsDO::getMerchantSn, "mch-1680001575858")
                .orderByDesc(MerchantProviderParamsDO::getMerchantSn)
                .last("limit 1000");
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = paramsDynamicMapper.selectList(lambdaQueryWrapper);
        CollectionWorker.of(merchantProviderParamsDOS).forEach(merchantProviderParamsDO -> {
            String merchantSn = merchantProviderParamsDO.getMerchantSn();
            try {
                Set<GroupCombinedStrategyDetailDO> detailDOS = mcRulesDecisionService.chooseGroupBySnAndOrg(merchantSn, null);
                log.info("商户:{}, 选择的收单机构: {}", merchantSn, detailDOS);
            } catch (Exception e) {
                log.error("exception: ", e);
            }
        });
    }

    @Autowired
    private UserRpcService userRpcService;

    @Test
    public void testUserPromotionOrganizationPath() {
        Map<String, UserBean> userMap = userRpcService.getUserByMerchantIds(Lists.newArrayList("00011078-6775-4633-865b-5cf31545198f"));
        List<String> collect = userMap.values().stream().map(userBean -> userBean.getOrganizationCodes().replaceAll("/", ",")).collect(Collectors.toList());
        assertThat(collect.get(0)).isEqualTo("00003,03054,10460,02264");
    }

}
