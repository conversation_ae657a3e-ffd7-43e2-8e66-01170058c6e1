package com.wosai.upay.job.biz;

import com.alibaba.fastjson.JSON;
import com.wosai.data.bean.BeanUtil;
import com.wosai.upay.job.BaseTest;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.service.ConfigSupportService;
import com.wosai.upay.merchant.contract.model.WeixinConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

public class AgentAppidBizTest extends BaseTest {

    @SpyBean
    private AgentAppidBiz agentAppidBiz;

    @Mock
    private ConfigSupportService configSupportService;

    @SpyBean
    private ApplicationApolloConfig applicationApolloConfig;

    private Map appidConfig;

    @Before
    public void before() {
        ReflectionTestUtils.setField(agentAppidBiz, "configSupportService", configSupportService);
        appidConfig = JSON.parseObject("{\"weixin_app_id\":\"wxc6ca2c84581752bb\",\"weixin_receipt_appid\":\"wxd2f16468474f61b8\",\"agent_name\":\"1020_3_*_false_true_0210\",\"weixin_mini_appid\":\"xx\"}\n");
    }

    @Test
    public void getConfigSuccess() {
        Mockito.doReturn("上海").when(configSupportService).getMerchantCity(any());
        Mockito.doReturn(appidConfig).when(agentAppidBiz).getConfigByKeyAndCity(any(), any());
        WeixinConfig weixinConfig = agentAppidBiz.getConfig("xx", 1016, "z", "x");
        assertNotNull(weixinConfig);
    }

    @Test
    public void getConfigFail() {
        Mockito.doReturn("上海").when(configSupportService).getMerchantCity(any());
        Mockito.doReturn(null).when(agentAppidBiz).getConfigByKeyAndCity(any(), any());
        WeixinConfig weixinConfig = agentAppidBiz.getConfig("xx", 1016, "z", "x");
        assertNull(weixinConfig);
    }

    @Test
    public void getAgentName() {
        Mockito.doReturn(appidConfig).when(agentAppidBiz).getConfigByKeyAndCity(any(), any());
        assertEquals("1020_3_*_false_true_0210", agentAppidBiz.getAgentName(3, 1016, "z", "x"));
    }

    @Test
    public void getConfigByKeyAndCity() {
        Mockito.doReturn(JSON.parseObject("{\"2_1016_36002013293\":{\"其他\":{\"agent_name\":\"1016_2_*_false_true_0779\"}}}")).when(applicationApolloConfig).getAgentAppidConfig();
        Map map = agentAppidBiz.getConfigByKeyAndCity("2_1016_36002013293", "xx");
        assertEquals("1016_2_*_false_true_0779", BeanUtil.getPropString(map, "agent_name"));
    }
}