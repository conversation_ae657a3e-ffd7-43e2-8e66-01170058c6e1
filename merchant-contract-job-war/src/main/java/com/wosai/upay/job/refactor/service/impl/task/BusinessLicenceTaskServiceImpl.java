package com.wosai.upay.job.refactor.service.impl.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.core.AcquirerOrgTypeEnum;
import com.shouqianba.cua.enums.core.BusinessLicenseTypeEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.DisableStatusEnum;
import com.shouqianba.cua.utils.stream.ExtCollectors;
import com.shouqianba.model.dto.request.MerchantTradeParamsDisableReasonManageReqDTO;
import com.shouqianba.model.enums.TradeParamsDisableReasonAccessSideEnum;
import com.shouqianba.model.enums.TradeParamsDisableReasonOperateTypeEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiMapUtils;
import com.wosai.common.utils.WosaiStringUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.enums.PayWayActivityMainProcessStatusEnum;
import com.wosai.mc.constants.CrmApplyConstant;
import com.wosai.mc.model.CommonResultResp;
import com.wosai.mc.model.MerchantBusinessLicenseInfo;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.mc.model.req.SaveOrUpdateLicenseReq;
import com.wosai.mc.service.MerchantBusinessLicenseService;
import com.wosai.mc.utils.PhotoUtils;
import com.wosai.sales.merchant.business.bean.app.request.GetFieldAppStatusReq;
import com.wosai.sales.merchant.business.bean.app.response.GetFieldAppStatusResp;
import com.wosai.sales.merchant.business.service.common.CommonFieldService;
import com.wosai.service.PaywayActivityService;
import com.wosai.upay.bank.info.api.model.BankInfo;
import com.wosai.upay.bank.info.api.service.BankInfoService;
import com.wosai.upay.bank.model.SameNameBankChangeConstant;
import com.wosai.upay.bank.model.verify.AmountVerify;
import com.wosai.upay.bank.model.verify.VerifyResp;
import com.wosai.upay.bank.service.AccountVerifyService;
import com.wosai.upay.common.bean.ListResult;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.MerchantBankAccount;
import com.wosai.upay.core.model.MerchantBankAccountPre;
import com.wosai.upay.core.model.MerchantBusinessLicence;
import com.wosai.upay.core.service.MerchantBankService;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.ContractParamsBiz;
import com.wosai.upay.job.biz.GrayMerchantSnBiz;
import com.wosai.upay.job.biz.acquirer.ComposeAcquirerBiz;
import com.wosai.upay.job.biz.paramContext.ParamContextBiz;
import com.wosai.upay.job.enume.ChannelEnum;
import com.wosai.upay.job.enume.ErrorMsgViewEndpointTypeEnum;
import com.wosai.upay.job.enume.microUpgrade.UpgradeVersion;
import com.wosai.upay.job.exception.ContextParamException;
import com.wosai.upay.job.model.ContractStatus;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.model.dto.crm.CheckResultForCrmInfoManageDTO;
import com.wosai.upay.job.model.dto.crm.CrmInformationManagementApplyFormDTO;
import com.wosai.upay.job.model.dto.crm.SubmitResultForCrmInfoManageDTO;
import com.wosai.upay.job.model.dto.crm.v3.MicroUpgradeDTO;
import com.wosai.upay.job.model.dto.microUpgrade.BusinessLicenseCertificationProcess;
import com.wosai.upay.job.model.dto.request.BusinessLicenseUpdateDTO;
import com.wosai.upay.job.model.dto.request.LicenseUpdateAmountVerifyReqDTO;
import com.wosai.upay.job.model.dto.request.UpdateCardInfoAfterMicroUpgradeDTO;
import com.wosai.upay.job.model.dto.response.*;
import com.wosai.upay.job.refactor.biz.acquirer.AcquirerFacade;
import com.wosai.upay.job.refactor.biz.acquirer.lklv3.LklV3AcquirerFacade;
import com.wosai.upay.job.refactor.dao.*;
import com.wosai.upay.job.refactor.model.bo.BusinessLicenseCertificationNodeInfo;
import com.wosai.upay.job.refactor.model.bo.InternalMicroUpgradeApplyExcelBO;
import com.wosai.upay.job.refactor.model.entity.*;
import com.wosai.upay.job.refactor.model.enums.BusinessLicenseCertificationNodeStatus;
import com.wosai.upay.job.refactor.model.enums.crm.CrmApplyFormAuditStatusEnum;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.task.license.LicenseUpgradeErrorMsgPromptConvertor;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseAuditApplyDTO;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV3MainTaskContext;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseDTO;
import com.wosai.upay.job.refactor.task.license.crm.CrmLicenseApplyUpdate;
import com.wosai.upay.job.refactor.biz.merchant.MerchantBasicInfoBiz;
import com.wosai.upay.job.refactor.biz.params.MerchantTradeParamsBiz;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleMainTaskStatusEnum;
import com.wosai.upay.job.refactor.model.enums.InternalScheduleTaskTypeEnum;
import com.wosai.upay.job.refactor.service.factory.McJobThreadPoolFactory;
import com.wosai.upay.job.refactor.service.impl.InterScheduleTaskServiceImpl;
import com.wosai.upay.job.refactor.task.license.account.BankAccountVerifyTaskHandler;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationTask;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV2Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV2MainTaskContext;
import com.wosai.upay.job.refactor.task.license.micro.MicroUpgradeTaskHandler;
import com.wosai.upay.job.refactor.task.license.update.UpdateBusinessNameTaskHandler;
import com.wosai.upay.job.refactor.task.rotational.MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor;
import com.wosai.upay.job.refactor.task.rotational.RotationalTask;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalSubTaskTypeEnum;
import com.wosai.upay.job.refactor.task.rotational.entity.RotationalTaskContext;
import com.wosai.upay.job.refactor.utils.ThreadPoolWorker;
import com.wosai.upay.job.service.ErrorCodeManageService;
import com.wosai.upay.job.service.task.BusinessLicenceTaskService;
import com.wosai.upay.job.util.BatchChangeAcquireUtil;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import com.wosai.upay.merchant.contract.model.ContractResponse;
import com.wosai.upay.merchant.contract.model.Tuple2;
import com.wosai.upay.merchant.contract.model.provider.FuyouParam;
import com.wosai.upay.merchant.contract.model.provider.HaikeParam;
import com.wosai.upay.merchant.contract.model.provider.LklV3Param;
import com.wosai.upay.merchant.contract.service.FuyouService;
import com.wosai.upay.merchant.contract.service.HaikeService;
import com.wosai.upay.merchant.contract.service.LklV3Service;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import vo.ApiRequestParam;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 营业执照任务服务
 *
 * <AUTHOR>
 * @date 2024/9/6 09:16
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class BusinessLicenceTaskServiceImpl implements BusinessLicenceTaskService {


    @Resource
    private InterScheduleTaskServiceImpl interScheduleTaskService;

    @Resource
    private MerchantBasicInfoBiz merchantBasicInfoBiz;

    @Resource
    private BusinessLicenceCertificationTask businessLicenceCertificationTask;

    @Resource
    private BusinessLicenceCertificationV2Task businessLicenceCertificationV2Task;

    @Resource
    private InternalScheduleMainTaskDAO internalScheduleMainTaskDAO;

    @Resource
    private MerchantProviderParamsDAO merchantProviderParamsDAO;

    @Resource
    private ParamContextBiz paramContextBiz;

    @Autowired
    private BankInfoService bankInfoService;

    @Autowired
    private LklV3Service lklV3Service;

    @Autowired
    private HaikeService haikeService;

    @Autowired
    private FuyouService fuyouService;

    @Resource
    private ContractParamsBiz contractParamsBiz;

    @Autowired
    private MerchantBusinessLicenseService merchantBusinessLicenseService;

    @Resource
    private ComposeAcquirerBiz compressAcquirerBiz;

    @Resource
    private MerchantTradeParamsBiz merchantTradeParamsBiz;

    @Resource
    private LklV3AcquirerFacade lklV3AcquirerFacade;

    @Resource
    private McAcquirerDAO mcAcquirerDAO;

    @Autowired
    private CommonFieldService commonFieldService;

    private static final String CRM_BUSINESS_LICENSE_FIELD_KEY = "business_license2";

    @Autowired
    private AccountVerifyService accountVerifyService;

    @Resource
    private CrmLicenseApplyUpdate crmLicenseApplyUpdate;

    @Resource
    private ContractStatusDAO contractStatusDAO;

    @Autowired
    private PaywayActivityService paywayActivityService;

    @Resource
    private LicenseUpgradeErrorMsgPromptConvertor errorMsgPromptConvertor;
    @Autowired
    private GrayMerchantSnBiz grayMerchantSnBiz;

    @Resource
    private BusinessLicenceCertificationV3Task businessLicenceCertificationV3Task;


    /**
     * 当前商户是否有营业执照更新任务
     * 对于审批单处理中的校验，不可以调用该方法，因为该方法会判断审批单处理中的商户
     *
     * @param merchantSn 商户号
     * @return 是否有营业执照更新任务
     */
    @Override
    public Boolean hasLicenceUpdateNotFinishedTask(String merchantSn) {
        // 这段逻辑应该在营业执照认证v2上线之前就上线
        if (isExistedCrmLicenseApplyProcessing(merchantSn)) {
            return true;
        }
        return isExistedProcessingTask(merchantSn);
    }

    public Boolean isExistedCrmLicenseApplyProcessing(String merchantSn) {
        try {
            Optional<String> merchantIdOpt = merchantBasicInfoBiz.getMerchantIdByMerchantSn(merchantSn);
            if (!merchantIdOpt.isPresent()) {
                return false;
            }
            String merchantId = merchantIdOpt.get();
            GetFieldAppStatusReq getFieldAppStatusReq = new GetFieldAppStatusReq();
            getFieldAppStatusReq.setMerchantId(merchantId);
            getFieldAppStatusReq.setFieldType(CRM_BUSINESS_LICENSE_FIELD_KEY);
            GetFieldAppStatusResp fieldAppStatus = commonFieldService.getFieldAppStatus(getFieldAppStatusReq);
            return Objects.nonNull(fieldAppStatus)
                    && (Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_INIT) ||
                    Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_PENDING));
        } catch (Exception e) {
            log.warn("isExistedCrmLicenseApplyProcessing error, merchantSn:{}", merchantSn, e);
            return false;
        }
    }

    private boolean isExistedProcessingTask(String merchantSn) {
        return interScheduleTaskService.isExistedProcessingTask(merchantSn,
                Lists.newArrayList(InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION,
                        InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V2,InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V3));
    }

    /**
     * 当前商户是否有营业执照更新任务v2
     *
     * @param merchantSn 商户号
     * @return 结果
     */
    @Override
    public Boolean hasLicenseUpdateNotFinishedV2Task(String merchantSn) {
        return interScheduleTaskService.isExistedProcessingTask(merchantSn,
                Lists.newArrayList(InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V2));
    }


    @Override
    public Boolean hasLicenseUpdateNotFinishedV3Task(String merchantSn) {
        return businessLicenceCertificationV3Task.isMicroUpgradeV3(merchantSn) && interScheduleTaskService.isExistedProcessingTask(merchantSn,
                Lists.newArrayList(InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V3));
    }

    @Override
    public Map<String, Object> getNotFinishedV3TaskContext(String merchantSn) {
        List<InternalScheduleMainTaskDO> mainTaskDOList = internalScheduleMainTaskDAO.listBySnAndTypes(merchantSn, Lists.newArrayList(InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V3.getValue()));
        if (mainTaskDOList != null) {
            final Optional<String> optional = mainTaskDOList.stream().filter(InternalScheduleMainTaskDO::taskInProcessing)
                    .findFirst().map(InternalScheduleMainTaskDO::getContext);
            if(optional.isPresent()) {
                Map<String, Object> contextMap = new HashMap<>();
                BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(optional.get(),
                        BusinessLicenseCertificationV3MainTaskContext.class);
                contextMap.put(ParamContextBiz.KEY_MERCHANT, mainTaskContextBOInner.getMerchant());
                contextMap.put(ParamContextBiz.KEY_BUSINESS_LICENCE, mainTaskContextBOInner.getMerchantBusinessLicense());
                contextMap.put(ParamContextBiz.KEY_BANK_INFO, mainTaskContextBOInner.getBankInfo());
                contextMap.put(ParamContextBiz.KEY_BANK_ACCOUNT, mainTaskContextBOInner.getBankAccount());
                return contextMap;
            }
        }
        return Collections.emptyMap();
    }

    @Resource
    private ContractTaskDAO contractTaskDAO;
    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    @Override
    public List<BusinessLicenseCertificationProcess> showBusinessLicenseCertificationProcess(String merchantSn, ErrorMsgViewEndpointTypeEnum endpointType) {
        List<BusinessLicenseCertificationProcess> processList = new ArrayList<>();
        final Boolean notFinishedV2Task = hasLicenseUpdateNotFinishedV2Task(merchantSn);
        final Boolean notFinishedV3Task = hasLicenseUpdateNotFinishedV3Task(merchantSn);
        if (!notFinishedV2Task && !notFinishedV3Task) {
            return processList;
        }
        List<String> nodeNames = Arrays.asList("提交认证", "收单机构审核", "支付源商家认证", "微信/支付宝收款授权");
        int nodeCount = notFinishedV3Task ? 4 : 2;
        List<BusinessLicenseCertificationNodeInfo> nodeInfos = new ArrayList<>();
        String submitTime = "";
        int currentStep = 0;
        String failReason = null;
        // 业务状态推导
        if (notFinishedV2Task) {
            final Optional<InternalScheduleMainTaskDO> mainTaskDO = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V2.getValue())
                    .stream()
                    .filter(InternalScheduleMainTaskDO::taskInProcessing)
                    .sorted(Comparator.comparing(InternalScheduleMainTaskDO::getMtime).reversed())
                    .findFirst();
            if (!mainTaskDO.isPresent()) {
                log.warn("商户号:{},V2流程没有找到最新的营业执照申请流程", merchantSn);
                return Collections.emptyList();
            }
            BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.get().getContext(),
                    BusinessLicenseCertificationV2MainTaskContext.class);
            BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
            Integer fieldAppInfoId = auditApplyDTO.getFieldAppInfoId();
            Map<String, Object> fieldAppInfoByAppId = commonFieldService.getFieldAppInfoByAppId(fieldAppInfoId);
            if (fieldAppInfoByAppId != null && fieldAppInfoByAppId.get("open_submit_time") != null) {
                submitTime = String.valueOf(fieldAppInfoByAppId.get("open_submit_time"));
            }
            currentStep = 1;

        } else if (notFinishedV3Task) {
            final Optional<InternalScheduleMainTaskDO> mainTaskDO = internalScheduleMainTaskDAO.listBySnAndType(merchantSn, InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V3.getValue())
                    .stream()
                    .filter(InternalScheduleMainTaskDO::taskInProcessing)
                    .sorted(Comparator.comparing(InternalScheduleMainTaskDO::getMtime).reversed())
                    .findFirst();
            if (!mainTaskDO.isPresent()) {
                log.warn("商户号:{},V3流程没有找到最新的营业执照申请流程", merchantSn);
                return Collections.emptyList();
            }
            BusinessLicenseCertificationV3MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.get().getContext(),
                    BusinessLicenseCertificationV3MainTaskContext.class);
            BusinessLicenseAuditApplyDTO auditApplyDTO = mainTaskContextBOInner.getAuditApplyDTO();
            Integer fieldAppInfoId = auditApplyDTO.getFieldAppInfoId();
            Map<String, Object> fieldAppInfoByAppId = commonFieldService.getFieldAppInfoByAppId(fieldAppInfoId);
            if (fieldAppInfoByAppId != null && fieldAppInfoByAppId.get("open_submit_time") != null) {
                //间戳转换为 LocalDateTime
                LocalDateTime dateTime = LocalDateTime.ofInstant(
                        Instant.ofEpochMilli((Long) fieldAppInfoByAppId.get("open_submit_time")),
                        ZoneId.systemDefault()
                );
                // 定义日期时间格式
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                submitTime =  dateTime.format(formatter);
            }
            currentStep = 1;
            final Long mainContractTaskId = mainTaskContextBOInner.getMainContractTaskId();
            if (mainContractTaskId != null) {
                final Optional<ContractTaskDO> contractTaskDO = contractTaskDAO.getByPrimaryKey(mainContractTaskId);
                if (contractTaskDO.isPresent()) {
                    final Integer status = contractTaskDO.map(ContractTaskDO::getStatus).get();
                    if (Objects.equals(status, ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())) {
                        currentStep = 2;
                        final Optional<ContractTaskDO> wxAuthenticationTask = contractTaskDAO.getLastedTaskBySnAndType(merchantSn, ContractTaskTypeEnum.WECHAT_BUSINESS_CERTIFICATION.getValue());
                        final Optional<ContractTaskDO> aliAuthenticationTask = contractTaskDAO.getLastedTaskBySnAndType(merchantSn, ContractTaskTypeEnum.ALIPAY_BUSINESS_CERTIFICATION.getValue());
                        Integer wxAuthenticationStatus = null;
                        Integer aliAuthenticationStatus = null;
                        if (wxAuthenticationTask.isPresent()) {
                            wxAuthenticationStatus = wxAuthenticationTask.get().getStatus();
                        }
                        if (aliAuthenticationTask.isPresent()) {
                            aliAuthenticationStatus = aliAuthenticationTask.get().getStatus();
                        }
                        if (Objects.equals(wxAuthenticationStatus, ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue()) &&
                                Objects.equals(aliAuthenticationStatus, ContractTaskProcessStatusEnum.PROCESS_SUCCESS.getValue())) {
                            currentStep = 3;
                            if (Boolean.TRUE.equals(mainTaskContextBOInner.getAliAuthStatus()) && Boolean.TRUE.equals(mainTaskContextBOInner.getWxAuthStatus())) {
                                currentStep = 4;
                            }
                        } else if ((Objects.equals(wxAuthenticationStatus, ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue())) ||
                                (Objects.equals(aliAuthenticationStatus, ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue()))) {
                            failReason = "支付源商家认证失败";
                        }
                    } else if (Objects.equals(status, ContractTaskProcessStatusEnum.PROCESS_FAIL.getValue())) {
                        failReason = "收单机构审核失败";
                    }
                }
            }
        }
        // 推导每个节点状态
        for (int i = 0; i < nodeCount; i++) {
            BusinessLicenseCertificationNodeStatus status;
            String time = null;
            String fail = null;
            if (failReason != null && i == currentStep) {
                status = BusinessLicenseCertificationNodeStatus.FAILED;
                fail = failReason;
            } else if (i < currentStep) {
                status = BusinessLicenseCertificationNodeStatus.COMPLETED;
                if (i == 0) time = submitTime;
            } else if (i == currentStep) {
                status = BusinessLicenseCertificationNodeStatus.IN_PROGRESS;
            } else {
                status = BusinessLicenseCertificationNodeStatus.NOT_STARTED;
            }
            nodeInfos.add(new BusinessLicenseCertificationNodeInfo(nodeNames.get(i), status, time, fail));
        }
        // 组装DTO
        for (int i = 0; i < nodeInfos.size(); i++) {
            BusinessLicenseCertificationNodeInfo node = nodeInfos.get(i);
            String prompt = buildPrompt(node, endpointType);
            boolean show = i <= currentStep;
            processList.add(new BusinessLicenseCertificationProcess(node.name, show, prompt));
        }
        return processList;
    }

    // prompt生成方法
    private String buildPrompt(BusinessLicenseCertificationNodeInfo node, ErrorMsgViewEndpointTypeEnum endpointType) {
        switch (node.status) {
            case COMPLETED:
                return node.time != null ? "提交成功 " + node.time : "已完成";
            case IN_PROGRESS:
                if ("收单机构审核".equals(node.name)) {
                    return getUpgradeCheckPromptMessage(endpointType, "进件审核中");
                } else {
                    return node.name + "中";
                }
            case FAILED:
                return "失败，原因：" + (node.failReason != null ? node.failReason : "");
            default:
                return "";
        }
    }




    @Autowired
    private ErrorCodeManageService errorCodeManageService;

    public static final String LICENSE_UPGRADE_CHECK_PLATFORM = "merchant_center_license_upgrade_check";

    /**
     * 获取小微升级校验失败提示信息
     *
     * @param endpointType 展示端
     * @param message      原始信息
     * @return 转义后的信息
     */
    public String getUpgradeCheckPromptMessage(ErrorMsgViewEndpointTypeEnum endpointType, String message) {
        return getPromptMsg(endpointType, message, LICENSE_UPGRADE_CHECK_PLATFORM);
    }

    private String getPromptMsg(ErrorMsgViewEndpointTypeEnum endpointType, String message, String licenseUpgradeCheckPlatform) {
        try {
            List<ErrorInfoPromptTextRspDTO> promptMessages = errorCodeManageService.getAllEndPointPromptMessage(message, licenseUpgradeCheckPlatform);
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(promptMessages)) {
                return message;
            }
            String finalMessage = message;
            Map<ErrorMsgViewEndpointTypeEnum, String> endpointTypeEnumStringMap = promptMessages.stream()
                    .collect(ExtCollectors.toMap(ErrorInfoPromptTextRspDTO::getErrorMsgViewEndpointTypeEnum,
                            t -> Objects.nonNull(t) && StringUtils.isNotBlank(t.getPromptText()) ? t.getPromptText() : finalMessage,
                            (t1, t2) -> t1));
            if (MapUtils.isNotEmpty(endpointTypeEnumStringMap)) {
                message = endpointTypeEnumStringMap.getOrDefault(endpointType, message);
            }
            return message;
        } catch (Exception e) {
            log.error("获取营业执照认证提示信息失败, endpointType:{}, message:{}", endpointType, message, e);
            return message;
        }
    }


    /**
     * 新增营业执照更新(认证）任务
     * 任务内容：重新进件（如果是小微升级），通知风控， 更新营业执照信息（还有涉及银行卡）
     *
     * @param updateReqDTO 营业执照更新请求
     */
    @Override
    public InsertLicenceUpdateTaskResultRspDTO insertLicenceUpdateTask(BusinessLicenseUpdateDTO updateReqDTO) {
        if (Objects.isNull(updateReqDTO) || Objects.isNull(updateReqDTO.getMerchantId())) {
            log.error("营业执照信息不完整，商户号为空, req:{}", updateReqDTO);
            return InsertLicenceUpdateTaskResultRspDTO.fail("营业执照信息不完整");
        }
        updateReqDTO.setNeedNotifyRiskResultStatus(BusinessLicenseUpdateDTO.NEED_NOTIFY_RISK_RESULT);
        Optional<String> merchantSn = merchantBasicInfoBiz.getMerchantSnByMerchantId(updateReqDTO.getMerchantId());
        if (!merchantSn.isPresent()) {
            log.error("商户{}不存在", updateReqDTO.getMerchantId());
            return InsertLicenceUpdateTaskResultRspDTO.fail("商户信息错误");
        }
        boolean existedProcessingTask = interScheduleTaskService.isExistedProcessingTask(merchantSn.get(), InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION);
        if (existedProcessingTask) {
            log.error("商户{}存在正在进行中的营业执照认证任务", merchantSn.get());
            return InsertLicenceUpdateTaskResultRspDTO.fail("营业执照认证中，请勿重复提交");
        }
        try {
            businessLicenceCertificationTask.insertMainTaskForUpdateTask(merchantSn.get(), updateReqDTO);
            return InsertLicenceUpdateTaskResultRspDTO.success();
        } catch (Exception e) {
            log.error("新增营业执照认证任务失败, 商户号:{}", merchantSn.get(), e);
            return InsertLicenceUpdateTaskResultRspDTO.fail("新增营业执照认证任务失败");
        }
    }

    /**
     * 针对收钱吧非小微商户，新增营业执照更新(认证）任务
     *
     * @param merchantSn 商户号
     * @return 响应结果
     */
    @Override
    public InsertLicenceUpdateTaskResultRspDTO insertLicenceUpdateTaskForNotMicroInSQB(String merchantSn) {
        if (merchantBasicInfoBiz.isMerchantLicenseMicro(merchantSn)) {
            return InsertLicenceUpdateTaskResultRspDTO.fail("商户在收钱吧是小微商户");
        }
        MerchantBusinessLicenseInfo licenseInfo = merchantBasicInfoBiz.getMerchantBusinessLicenseInfo(merchantSn);
        BusinessLicenseUpdateDTO updateDTO = buildBusinessLicenseUpdateDTO(licenseInfo);
        updateDTO.setNeedNotifyRiskResultStatus(BusinessLicenseUpdateDTO.NOT_NEED_NOTIFY_RISK_RESULT);
        return businessLicenceCertificationTask.internalBuildTask(merchantSn, updateDTO);
    }

    private BusinessLicenseUpdateDTO buildBusinessLicenseUpdateDTO(MerchantBusinessLicenseInfo licenseInfo) {
        BusinessLicenseUpdateDTO updateDTO = new BusinessLicenseUpdateDTO();
        updateDTO.setMerchantId(licenseInfo.getMerchant_id());
        updateDTO.setPhoto(licenseInfo.getPhoto());
        updateDTO.setNumber(licenseInfo.getNumber());
        updateDTO.setName(licenseInfo.getName());
        updateDTO.setValidity(licenseInfo.getValidity());
        updateDTO.setLegalPersonName(licenseInfo.getLegal_person_name());
        updateDTO.setLicenseAddress(licenseInfo.getAddress());
        updateDTO.setLicenseType(licenseInfo.getType());
        updateDTO.setIdType(licenseInfo.getLegal_person_id_type());
        updateDTO.setLegalPersonIdCardFrontPhoto(licenseInfo.getLegal_person_id_card_front_photo());
        updateDTO.setLegalPersonIdCardBackPhoto(licenseInfo.getLegal_person_id_card_back_photo());
        updateDTO.setLegalPersonIdNumber(licenseInfo.getLegal_person_id_number());
        updateDTO.setLegalPersonIdCardAddress(licenseInfo.getLegal_person_id_card_address());
        updateDTO.setLegalPersonIdCardIssuingAuthority(licenseInfo.getLegal_person_id_card_issuing_authority());
        updateDTO.setIdValidity(licenseInfo.getId_validity());
        return updateDTO;
    }

    /**
     * 批量针对收钱吧非小微商户，新增营业执照更新(认证）任务
     *
     * @param merchantSns 商户号列表
     */
    @Override
    public void batchInsertLicenceUpdateTasksForNotMicroInSQB(List<String> merchantSns) {
        if (CollectionUtils.isEmpty(merchantSns)) {
            return;
        }
        for (String merchantSn : merchantSns) {
            ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> insertLicenceUpdateTaskForNotMicroInSQB(merchantSn));
        }
    }

    /**
     * 判断商户是否需要重新进件
     * 当前为小微，就需要进件 否则不进件
     *
     * @param merchantSn 商户号
     * @return 是否需要进件 true-需要
     */
    @Override
    public Boolean isUpgradeNeedContractToAcquirer(String merchantSn) {
        return businessLicenceCertificationTask.isUpgradeNeedContractToAcquirer(merchantSn);
    }

    /**
     * 通知风控人工审核结果
     *
     * @param manualAuditPass 人工审核是否通过 true-通过
     * @param merchantSn      商户号
     */
    @Override
    public void notifyRiskManualLicenceAuditResult(String merchantSn, boolean manualAuditPass) {
        ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> doNotifyRiskManualLicenceAuditResult(merchantSn, manualAuditPass));
    }

    private void doNotifyRiskManualLicenceAuditResult(String merchantSn, boolean manualAuditPass) {
        if (manualAuditPass) {
            businessLicenceCertificationTask.riskManualLicenceAuditPass(merchantSn);
            return;
        }
        businessLicenceCertificationTask.riskManualLicenceAuditReject(merchantSn);
    }

    /**
     * 商户是否正在做小微升级
     *
     * @param merchantSn 商户号
     * @return 是否正在做小微升级 true-是
     */
    @Override
    public Boolean isMerchantDoingMicroUpgrade(String merchantSn) {
        return internalScheduleMainTaskDAO.listBySnAndType(merchantSn, InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION.getValue())
                .stream().anyMatch(t -> !Objects.equals(t.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue())
                        && !Objects.equals(t.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_FAIL.getValue())
                        && Objects.equals(t.getEnableScheduledStatus(), ScheduleStatusEnum.CAN.getValue()));
    }

    /**
     * 是否可以提交已禁用商户换卡审批
     * 如果不可以，抛出异常（适配审批中心接口标准）
     *
     * @param paramsMap 参数
     */
    @Override
    public void isCanSubmitDisableMerchantChangeCardAudit(Map paramsMap) {
        String merchantSn = getMerchantSnFromAuditParams(paramsMap);
        String acquirerMerchantId = MapUtils.getString(paramsMap, "acquirerMerchantId");
        if (StringUtils.isBlank(acquirerMerchantId)) {
            throw new ContractBizException("收单机构商户号为空");
        }
        Map<String, MerchantProviderParamsDO> payMerchantIdParamsMap = getDisabledAcquirerMerchantIdParmasMap(merchantSn);
        if (!payMerchantIdParamsMap.containsKey(acquirerMerchantId)) {
            throw new ContractBizException("收单机构商户号未被禁用");
        }
    }

    /**
     * 查询由于小微升级而被逻辑删除的交易参数
     *
     * @param merchantSn 商户号
     * @return 删除的参数列表
     */
    @Override
    public List<AlreadyDeletedParamsByMicroUpgradeRspDTO> listDeletedParamsByMicroUpgrade(String merchantSn) {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantProviderParamsDAO.listDeletedParamsBySn(merchantSn)
                .stream().filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(merchantProviderParamsDOS)) {
            return Collections.emptyList();
        }
        return merchantProviderParamsDOS.stream().map(this::getAlreadyDeletedParamsByMicroUpgradeRspDTO)
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
    }

    private Optional<AlreadyDeletedParamsByMicroUpgradeRspDTO> getAlreadyDeletedParamsByMicroUpgradeRspDTO(MerchantProviderParamsDO paramsDO) {
        try {
            AlreadyDeletedParamsByMicroUpgradeRspDTO rsp = new AlreadyDeletedParamsByMicroUpgradeRspDTO();
            if (StringUtils.isBlank(paramsDO.getExtra())) {
                return Optional.empty();
            }
            rsp.setDisableReasons(merchantTradeParamsBiz.getDisableReasons(paramsDO.getExtra().getBytes(StandardCharsets.UTF_8)));
            List<Object> disableReasons = merchantTradeParamsBiz.getDisableReasons(paramsDO.getExtra().getBytes(StandardCharsets.UTF_8));
            Optional<Object> any = disableReasons.stream().filter(disableReason -> {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(disableReason));
                return StringUtils.equals(MapUtils.getString(jsonObject, MerchantProviderParamsDO.DISABLE_REASON_KEY),
                        BusinessLicenceCertificationTask.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON);
            }).findAny();
            if (!any.isPresent()) {
                return Optional.empty();
            }
            McAcquirerDO acquirerDO = mcAcquirerDAO.getByProvider(String.valueOf(paramsDO.getProvider()));
            rsp.setMerchantSn(paramsDO.getMerchantSn());
            rsp.setAcquirer(acquirerDO.getAcquirer());
            rsp.setAcquirerName(acquirerDO.getName());
            rsp.setAcquirerMerchantId(paramsDO.getProviderMerchantId());
            rsp.setContractTime(paramsDO.getCtime());
            rsp.setDisableStatus(paramsDO.getDisableStatus());
            return Optional.of(rsp);
        } catch (Exception e) {
            log.error("getAlreadyDeletedParamsByMicroUpgradeRspDTO error, params:{}", JSON.toJSONString(paramsDO), e);
            return Optional.empty();
        }
    }

    private String getMerchantSnFromAuditParams(Map paramsMap) {
        if (org.apache.commons.collections.MapUtils.isEmpty(paramsMap) || !paramsMap.containsKey("merchantSn")) {
            throw new CommonPubBizException("参数错误");
        }
        Map merchant = (Map) paramsMap.get("merchantSn");
        Map merchantSnData = (Map) merchant.get("merch_info.sn");
        if (org.apache.commons.collections.MapUtils.isEmpty(merchantSnData)) {
            throw new CommonPubBizException("商户信息为空");
        }
        String merchantSn = (String) merchantSnData.get("value");
        if (WosaiStringUtils.isBlank(merchantSn)) {
            throw new CommonPubBizException("商户号不可为空!");
        }
        return merchantSn;
    }

    @NotNull
    private Map<String, MerchantProviderParamsDO> getDisabledAcquirerMerchantIdParmasMap(String merchantSn) {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantProviderParamsDAO.listByMerchantSnWithoutDeletedStatus(merchantSn);
        Map<String, MerchantProviderParamsDO> payMerchantIdParamsMap = merchantProviderParamsDOS.stream()
                .filter(t -> Objects.isNull(t.getPayway()) || Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .filter(t -> Objects.equals(t.getDisableStatus(), DisableStatusEnum.DISABLE.getValue())
                        && StringUtils.contains(JSON.toJSONString(t.getExtra()), MicroUpgradeTaskHandler.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON))
                .collect(Collectors.toMap(MerchantProviderParamsDO::getPayMerchantId, Function.identity(), (a, b) -> a));
        Map<String, MerchantProviderParamsDO> providerMerchantIdParamsMap = merchantProviderParamsDOS.stream()
                .filter(t -> Objects.isNull(t.getPayway()) || Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .filter(t -> Objects.equals(t.getDisableStatus(), DisableStatusEnum.DISABLE.getValue())
                        && StringUtils.contains(JSON.toJSONString(t.getExtra()), MicroUpgradeTaskHandler.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON))
                .collect(Collectors.toMap(MerchantProviderParamsDO::getProviderMerchantId, Function.identity(), (a, b) -> a));
        Map<String, MerchantProviderParamsDO> paramsMap = new HashMap<>();
        paramsMap.putAll(providerMerchantIdParamsMap);
        paramsMap.putAll(payMerchantIdParamsMap);
        return paramsMap;
    }

    @Resource
    private RotationalTask rotationalTask;

    /**
     * 针对小微升级后，修改原商户号的结算卡信息
     * 目前只支持fuyou,haike和lklV3
     *
     * @param updateCardInfoAfterMicroUpgradeDTO 更新请求
     * @return 结果
     */
    @Override
    public CuaCommonResultDTO updateCardInfoAfterMicroUpgrade(UpdateCardInfoAfterMicroUpgradeDTO updateCardInfoAfterMicroUpgradeDTO) {
        String merchantSn = updateCardInfoAfterMicroUpgradeDTO.getMerchantSn();
        String acquirerMerchantId = updateCardInfoAfterMicroUpgradeDTO.getAcquirerMerchantId();
        Map<String, MerchantProviderParamsDO> payMerchantIdParamsMap = getDisabledAcquirerMerchantIdParmasMap(merchantSn);
        if (!payMerchantIdParamsMap.containsKey(acquirerMerchantId)) {
            log.warn("收单机构商户号不存在,不需要修改结算卡信息. 商户号:{},收单机构商户号:{}", merchantSn, acquirerMerchantId);
            return CuaCommonResultDTO.fail("收单机构商户号不存在");
        }
        MerchantProviderParamsDO paramsDO = payMerchantIdParamsMap.get(acquirerMerchantId);
        Integer provider = paramsDO.getProvider();
        if (!Objects.equals(provider, ProviderEnum.PROVIDER_HAIKE.getValue())
                && !Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue())
                && !Objects.equals(provider, ProviderEnum.PROVIDER_FUYOU.getValue())) {
            log.warn("不支持的收单机构，不需要修改结算卡信息. 商户号:{},收单机构商户号:{},provider:{}", merchantSn, acquirerMerchantId, provider);
            return CuaCommonResultDTO.fail("不支持的收单机构");
        }
        Map contextParam = buildUpdateDeletedAcquirerBankCardContext(merchantSn,
                updateCardInfoAfterMicroUpgradeDTO.getBankCardNo(),
                updateCardInfoAfterMicroUpgradeDTO.getOpenBankNo(),
                PhotoUtils.baseUrl(updateCardInfoAfterMicroUpgradeDTO.getBankCardPhoto()));
        if (Objects.equals(provider, ProviderEnum.PROVIDER_LAKALA_V3.getValue())) {
            return applpChangeCardToLkl(updateCardInfoAfterMicroUpgradeDTO, paramsDO, contextParam, merchantSn);
        }
        if (Objects.equals(provider, ProviderEnum.PROVIDER_HAIKE.getValue())) {
            return applyChangeCardToHaike(paramsDO, contextParam);
        }
        return applyChangeCardToFuYou(updateCardInfoAfterMicroUpgradeDTO, contextParam, paramsDO, merchantSn);
    }

    private CuaCommonResultDTO applyChangeCardToFuYou(UpdateCardInfoAfterMicroUpgradeDTO updateCardInfoAfterMicroUpgradeDTO, Map contextParam, MerchantProviderParamsDO paramsDO, String merchantSn) {
        FuyouParam fuyouParam = contractParamsBiz.buildContractParams(ChannelEnum.FUYOU.getValue(), FuyouParam.class);
        ContractResponse contractResponse = fuyouService.changeOfAccount(contextParam, paramsDO.getPayMerchantId(), fuyouParam);
        // response: {"trace_no":"00MAO3YI8P","modify_no":"*********","fy_mchnt_cd":"0002900F7800927","ret_msg":"操作成功","ret_code":"0000"}
        if (contractResponse.isSuccess()) {
            String modifyNo = MapUtils.getString(contractResponse.getResponseParam(), "modify_no");
            if (StringUtils.isNotBlank(modifyNo)) {
                RotationalTaskContext rotationalTaskContext = RotationalTaskContext.builder()
                        .belongToContractTask(false)
                        .subTaskTypeEnum(RotationalSubTaskTypeEnum.DISABLED_MERCHANT_GET_CHANGE_CARD_RESULT)
                        .merchantSn(merchantSn)
                        .rotationId(modifyNo)
                        .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.ACQUIRER_KEY, AcquirerTypeEnum.FU_YOU.getValue())
                        .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.AUDIT_ID_KEY, updateCardInfoAfterMicroUpgradeDTO.getAuditId())
                        .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.ACQUIRER_MERCHANT_ID, paramsDO.getPayMerchantId())
                        .build();
                rotationalTask.buildTaskForContractTask(rotationalTaskContext);
                return CuaCommonResultDTO.success("收单机构审核中");
            }
            return CuaCommonResultDTO.fail("提交富友接口换卡返回异常");
        }
        return CuaCommonResultDTO.fail("提交富友换卡请求失败");
    }

    private CuaCommonResultDTO applyChangeCardToHaike(MerchantProviderParamsDO paramsDO, Map contextParam) {
        HaikeParam haikeParam =  contractParamsBiz.buildContractParams(ChannelEnum.HAIKE.getValue(), HaikeParam.class);
        ContractResponse contractResponse = haikeService.updateBankAccountMerchant(paramsDO.getPayMerchantId(), contextParam, haikeParam);
        if (!contractResponse.isSuccess()) {
            return CuaCommonResultDTO.fail(contractResponse.getMessage());
        }
        return CuaCommonResultDTO.success("换卡成功");
    }

    private CuaCommonResultDTO applpChangeCardToLkl(UpdateCardInfoAfterMicroUpgradeDTO updateCardInfoAfterMicroUpgradeDTO, MerchantProviderParamsDO paramsDO, Map contextParam, String merchantSn) {
        LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        ContractResponse contractResponse = lklV3Service.updateMerchantBankAccount(paramsDO.getPayMerchantId(), contextParam, lklV3Param);
        if (!contractResponse.isSuccess()) {
            return CuaCommonResultDTO.fail(contractResponse.getMessage());
        }
        String contractId = MapUtils.getString(contractResponse.getTradeParam(), "contractId");
        if (StringUtils.isNotBlank(contractId)) {
            RotationalTaskContext rotationalTaskContext = RotationalTaskContext.builder()
                    .belongToContractTask(false)
                    .subTaskTypeEnum(RotationalSubTaskTypeEnum.DISABLED_MERCHANT_GET_CHANGE_CARD_RESULT)
                    .merchantSn(merchantSn)
                    .rotationId(contractId)
                    .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.ACQUIRER_KEY, AcquirerTypeEnum.LKL_V3.getValue())
                    .addParam(MicroUpgradeDisabledGetChangeCardResultSubTaskProcessor.AUDIT_ID_KEY, updateCardInfoAfterMicroUpgradeDTO.getAuditId())
                    .build();
            rotationalTask.buildTaskForContractTask(rotationalTaskContext);
            return CuaCommonResultDTO.success("收单机构审核中");
        }
        return CuaCommonResultDTO.success("换卡成功");
    }

    private Map buildUpdateDeletedAcquirerBankCardContext(String merchantSn, String bankCardNo, String openBankNo, String bankCardPhoto) {
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        Map bankAccountMap = MapUtils.getMap(contextParam, ParamContextBiz.KEY_BANK_ACCOUNT);
        if (MapUtils.isEmpty(bankAccountMap)) {
            throw new ContextParamException("商户银行卡信息不存在");
        }
        bankAccountMap.put(MerchantBankAccount.NUMBER, bankCardNo);
        bankAccountMap.put(MerchantBankAccount.OPENING_NUMBER, openBankNo);
        bankAccountMap.put(MerchantBankAccount.BANK_CARD_IMAGE, bankCardPhoto);
        if (StringUtils.isNotEmpty(openBankNo)) {
            Map bankInfo = bankInfoService.getBankInfo(CollectionUtil.hashMap(
                    BankInfo.OPENING_NUMBER, openBankNo
            ));
            if (WosaiMapUtils.isNotEmpty(bankInfo)) {
                contextParam.put(ParamContextBiz.KEY_BANK_INFO, bankInfo);
                bankAccountMap.put(MerchantBankAccount.BANK_NAME, MapUtils.getString(bankInfo, BankInfo.BANK_NAME));
                bankAccountMap.put(MerchantBankAccount.BRANCH_NAME, MapUtils.getString(bankInfo, BankInfo.BRANCH_NAME));
                bankAccountMap.put(MerchantBankAccount.OPENING_NUMBER, MapUtils.getString(bankInfo, BankInfo.OPENING_NUMBER));
                bankAccountMap.put(MerchantBankAccount.CLEARING_NUMBER, MapUtils.getString(bankInfo, BankInfo.CLEARING_NUMBER));
            }
        }
        Map businessMap = MapUtils.getMap(contextParam, ParamContextBiz.KEY_BUSINESS_LICENCE);
        businessMap.put(MerchantBusinessLicence.TYPE, BusinessLicenseTypeEnum.MICRO.getValue());
        return contextParam;
    }


    /**
     * 内部洗数据接口
     * 存量商户重新触发小微升级
     * 对于拉卡拉：收钱吧非小微，lkl侧是小微
     * 对于富友：触发小微升级 （富友的背景是什么，需要做校验吗）
     *
     * @param merchantSn 商户号
     * @param acquirer   收单机构 如果已经切走了，把之前的商户号禁用掉（禁用原因：小微升级）
     */
    @Override
    public CuaCommonResultDTO internalTriggerMicroUpgrade(String merchantSn, String acquirer) {
        boolean existed = isExistedProcessingTask(merchantSn);
        if (existed) {
            return CuaCommonResultDTO.fail("营业执照认证中，请勿重复提交");
        }
        if (merchantBasicInfoBiz.isMerchantLicenseMicro(merchantSn)) {
            log.error("商户在收钱吧是小微商户, merchantSn:{}", merchantSn);
            return CuaCommonResultDTO.fail("提交升级任务失败，原因：商户在收钱吧是小微商户");
        }
        if (StringUtils.equals(acquirer, AcquirerTypeEnum.LKL_V3.getValue())) {
            boolean microOnAcquirerSide = lklV3AcquirerFacade.isMicroOnAcquirerSide(merchantSn);
            if (!microOnAcquirerSide) {
                log.error("商户在拉卡拉是非小微商户, merchantSn:{}", merchantSn);
                return CuaCommonResultDTO.fail("提交升级任务失败，原因：商户在拉卡拉已经是非小微商户");
            }
        }
        String inUseAcquirer = compressAcquirerBiz.getMerchantAcquirer(merchantSn);
        if (!StringUtils.equals(acquirer, inUseAcquirer)) {
            log.error("不需要升级，商户已经切换收单机构, merchantSn:{}, originalAcquirer{}, inUseAcquirer:{}", merchantSn, acquirer, inUseAcquirer);
            return CuaCommonResultDTO.fail("不需要升级，商户已经切换收单机构");
        }
        MerchantBusinessLicenseInfo licenseInfo = merchantBasicInfoBiz.getMerchantBusinessLicenseInfo(merchantSn);
        SaveOrUpdateLicenseReq saveOrUpdateLicenseReq = buildSaveOrUpdateLicenseReq(licenseInfo);
         CommonResultResp rsp = merchantBusinessLicenseService.isEnableSubmitLicenseInternal(saveOrUpdateLicenseReq);
        if (!rsp.getResult()) {
            return CuaCommonResultDTO.fail("提交升级任务失败，原因：" + rsp.getMessage());
        }
        BusinessLicenseUpdateDTO businessLicenseUpdateDTO = buildBusinessLicenseUpdateDTO(licenseInfo);
        businessLicenseUpdateDTO.setNeedNotifyRiskResultStatus(BusinessLicenseUpdateDTO.NOT_NEED_NOTIFY_RISK_RESULT);
        InsertLicenceUpdateTaskResultRspDTO insertResult = businessLicenceCertificationTask.internalBuildTask(merchantSn, businessLicenseUpdateDTO);
        return insertResult.isSuccess() ? CuaCommonResultDTO.success() : CuaCommonResultDTO.fail(insertResult.getMessage());
    }

    /**
     * 内部洗数据接口
     *
     * @param fileUrl    文件地址
     * @return 结果
     */
    @Override
    public CuaCommonResultDTO internalTriggerMicroUpgradeByFile(String fileUrl) {
        try {
            List<InternalMicroUpgradeApplyExcelBO> merchantSns = BatchChangeAcquireUtil.getExcelInfoList(fileUrl, new InternalMicroUpgradeApplyExcelBO());
            if (CollectionUtils.isEmpty(merchantSns)) {
                return CuaCommonResultDTO.fail("文件中没有有效的商户号");
            }
            List<String> distinctMerchantSns = merchantSns.stream()
                    .filter(Objects::nonNull)
                    .map(InternalMicroUpgradeApplyExcelBO::getMerchantSn)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(distinctMerchantSns)) {
                return CuaCommonResultDTO.fail("文件中没有有效的商户号");
            }
            if (distinctMerchantSns.size() > 200) {
                log.warn("文件中商户号超过200个,截取前200个商户, merchantSnFileOssUrl:{}", fileUrl);
                distinctMerchantSns = distinctMerchantSns.subList(0, 200);
            }
            List<String> finalDistinctMerchantSns = distinctMerchantSns;
            ThreadPoolWorker.of(McJobThreadPoolFactory.getInstance()).submit(() -> {
                        for (String distinctMerchantSn : finalDistinctMerchantSns) {
                            try {
                                internalTriggerMicroUpgrade(distinctMerchantSn);
                                Thread.sleep(50);
                            } catch (Exception e) {
                                log.error("内部构建小微升级任务异常, merchantSn:{}", distinctMerchantSn, e);
                            }
                        }
                    });
            return CuaCommonResultDTO.success();
        } catch (Exception e) {
            log.error("文件获取商户信息失败, merchantSnFileOssUrl:{}", fileUrl, e);
            return CuaCommonResultDTO.fail("附件解析失败：" + e.getMessage());
        }
    }

    /**
     * 内部洗数据接口
     * 存量商户重新触发小微升级
     *
     * @param merchantSn 商户号
     * @return 结果
     */
    @Override
    public CuaCommonResultDTO internalTriggerMicroUpgrade(String merchantSn) {
        Optional<ContractStatusDO> contractStatusDOOpt = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatusDOOpt.isPresent()) {
            return CuaCommonResultDTO.fail("商户尚未进件成功");
        }
        String acquirer = contractStatusDOOpt.get().getAcquirer();
        if (!StringUtils.equals(acquirer, AcquirerTypeEnum.FU_YOU.getValue())
                && !StringUtils.equals(acquirer, AcquirerTypeEnum.HAI_KE.getValue())
                && !StringUtils.equals(acquirer, AcquirerTypeEnum.LKL_V3.getValue())) {
            return CuaCommonResultDTO.fail("不支持的收单机构");
        }
        return internalTriggerMicroUpgrade(merchantSn, acquirer);
    }

    private SaveOrUpdateLicenseReq buildSaveOrUpdateLicenseReq(MerchantBusinessLicenseInfo licenseInfo) {
        SaveOrUpdateLicenseReq saveOrUpdateLicenseReq = new SaveOrUpdateLicenseReq();
        saveOrUpdateLicenseReq.setMerchant_id(licenseInfo.getMerchant_id());
        saveOrUpdateLicenseReq.setPhoto(licenseInfo.getPhoto());
        saveOrUpdateLicenseReq.setNumber(licenseInfo.getNumber());
        saveOrUpdateLicenseReq.setName(licenseInfo.getName());
        saveOrUpdateLicenseReq.setValidity(licenseInfo.getValidity());
        saveOrUpdateLicenseReq.setLegal_person_name(licenseInfo.getLegal_person_name());
        saveOrUpdateLicenseReq.setLicense_address(licenseInfo.getAddress());
        saveOrUpdateLicenseReq.setLicense_type(licenseInfo.getType());
        saveOrUpdateLicenseReq.setId_type(licenseInfo.getLegal_person_id_type());
        saveOrUpdateLicenseReq.setLegal_person_id_card_front_photo(licenseInfo.getLegal_person_id_card_front_photo());
        saveOrUpdateLicenseReq.setLegal_person_id_card_back_photo(licenseInfo.getLegal_person_id_card_back_photo());
        saveOrUpdateLicenseReq.setLegal_person_id_number(licenseInfo.getLegal_person_id_number());
        saveOrUpdateLicenseReq.setLegal_person_id_card_address(licenseInfo.getLegal_person_id_card_address());
        saveOrUpdateLicenseReq.setLegal_person_id_card_issuing_authority(licenseInfo.getLegal_person_id_card_issuing_authority());
        saveOrUpdateLicenseReq.setId_validity(licenseInfo.getId_validity());
        return saveOrUpdateLicenseReq;
    }

    /**
     * 临时接口 更新营业执照-法人是执行事务合伙人 目前只支持拉卡拉
     * 法人证件类型改成执行事务合伙人上送拉卡拉：13
     * 法人证件号改成商户执照号
     * 法人证件姓名改成执行事务合伙人后面的公司（不用加委派代表）
     *
     * @param merchantSn          商户号
     * @param providerMerchantId  渠道商户号
     * @param legalPersonName     法人名称（执行事务合伙人后面的公司（不用加委派代表））
     * @param legalPersonIdNumber 法人证件号
     * @return 执行结果
     */
    @Override
    public CuaCommonResultDTO updateBusinessLicenceForExecutivePartner(String merchantSn, String providerMerchantId, String legalPersonName, String legalPersonIdNumber) {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantTradeParamsBiz.listParamsByMerchantSn(merchantSn);
        if (CollectionUtils.isEmpty(merchantProviderParamsDOS)) {
            return CuaCommonResultDTO.fail("商户交易参数为空");
        }
        // 目前只支持拉卡拉
        McAcquirerDO lklAcquirer = mcAcquirerDAO.getByAcquirer(AcquirerTypeEnum.LKL_V3.getValue());
        Optional<MerchantProviderParamsDO> paramsOpt = merchantProviderParamsDOS.stream().filter(paramsDO
                -> Objects.equals(paramsDO.getProvider(), Integer.valueOf(lklAcquirer.getProvider()))
                && Objects.equals(paramsDO.getPayway(), PaywayEnum.ACQUIRER.getValue())).findAny();
        if (!paramsOpt.isPresent()) {
            return CuaCommonResultDTO.fail("商户交易参数为空");
        }
        Map<String, Object> contextParam = paramContextBiz.getParamContextByMerchantSn(merchantSn, null);
        LklV3Param lklV3Param = contractParamsBiz.buildContractParams(ChannelEnum.LKLV3.getValue(), LklV3Param.class);
        ContractResponse contractResponse = lklV3Service.updateBusinessLicenseForExecutivePartner(paramsOpt.get().getPayMerchantId(),
                contextParam, lklV3Param ,legalPersonName,legalPersonIdNumber);
        return contractResponse.isSuccess() ? CuaCommonResultDTO.success() : CuaCommonResultDTO.fail(contractResponse.getMessage());
    }

    /**
     * 校验内容：
     *      1. 经营名称校验(如果修改了经营名称）
     *          30天修改 (不仅仅要查经营名称的审批单，还要查询营业执照审批单中更新经营名称的审批单)
     *          开通间连扫码
     *      2. 如果是小微，是否可以小微升级
     *      3. 存在卡验证中状态
     *      4. 存在营业执照变更任务
     * @return 校验结果
     */
    @Override
    public CheckResultForCrmInfoManageDTO checkSubmitBusinessLicenseApplyToContractSideAudit(CrmInformationManagementApplyFormDTO applyReq) {
        String merchantId = applyReq.getMerchantId();
        MerchantInfo merchantInfo = merchantBasicInfoBiz.getMerchantInfoById(merchantId);
        if (Objects.isNull(merchantInfo)) {
            return CheckResultForCrmInfoManageDTO.fail("商户不存在");
        }
        String merchantSn = merchantInfo.getSn();
        try {
            BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO = checkAndConvertApplyValidity(applyReq);
            checkContractSuccess(merchantSn);
            checkExistedLicenseUpdateTask(merchantSn);
            checkCanUpdateBusinessName(applyReq, merchantInfo);
            checkBusinessLicenseType(businessLicenseAuditApplyDTO, merchantInfo);
            if (merchantBasicInfoBiz.isMerchantLicenseMicro(merchantSn)) {
                checkCanMicroUpgrade(merchantSn);
            } else {
                checkCanUpdateLicense(merchantSn);
            }
            checkVerifyingBankCard(merchantInfo);
        } catch (ContractBizException e) {
            log.warn("checkSubmitBusinessLicenseApplyToContractSideAudit 业务校验失败, merchantId:{}, applyReq:{}", merchantId, JSON.toJSONString(applyReq), e);
            return CheckResultForCrmInfoManageDTO.fail(errorMsgPromptConvertor.getAppUpgradeCheckFailPromptMessage(e.getMessage(), e.getMessage()));
        } catch (Exception e) {
            log.error("checkSubmitBusinessLicenseApplyToContractSideAudit error, merchantId:{}, applyReq:{}", merchantId, JSON.toJSONString(applyReq), e);
            return CheckResultForCrmInfoManageDTO.fail(LicenseUpgradeErrorMsgPromptConvertor.DEFAULT_MESSAGE);
        }
        return CheckResultForCrmInfoManageDTO.success();
    }

    @Autowired
    private MerchantBankService merchantBankService;

    private void checkVerifyingBankCard(MerchantInfo merchantInfo) {
        String merchantId = merchantInfo.getId();
        ListResult listResult = merchantBankService.findMerchantBankAccountPres(null, CollectionUtil.hashMap(MerchantBankAccountPre.MERCHANT_ID, merchantId));
        if (listResult.getTotal() > 0) {
            for (Map bank : listResult.getRecords()) {
                int status = BeanUtil.getPropInt(bank, MerchantBankAccountPre.VERIFY_STATUS, -1);
                int processStatus = BeanUtil.getPropInt(bank, "extra.process_status", -1);
                if (status == MerchantBankAccountPre.VERIFY_STATUS_INPROGRESS ||
                        processStatus == SameNameBankChangeConstant.PROCESS_STATUS_ACCOUNT_VERIFYING ||
                        processStatus == SameNameBankChangeConstant.PROCESS_STATUS_VERIFY_AMOUNT) {
                    throw new ContractBizException("结算银行卡在验证中，暂时不可变更默认结算银行卡。");
                }
            }
        }
    }


    private void checkBusinessLicenseType(BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO, MerchantInfo merchantInfo) {
        BusinessLicenseDTO businessLicense = businessLicenseAuditApplyDTO.getBusinessLicense();
        String name = businessLicense.getName();
        String licenseCode = businessLicense.getNumber();
        if (StringUtils.isBlank(licenseCode)) {
            throw new ContractBizException("营业执照编码不能为空");
        }
        //  92 开头的必须含 "公司"
        if (BusinessLicenseTypeEnum.ENTERPRISE.getValue().equals(businessLicense.getType()) && licenseCode.startsWith("92")) {
            checkNameContainsCompany(name, "企业类型营业执照，名称必须包含'公司'字样'");
        }
    }

    private void checkNameContainsCompany(String name, String errorMessage) {
        if (StringUtils.isBlank(name) || !name.contains("公司")) {
            throw new ContractBizException(errorMessage);
        }
    }


    private BusinessLicenseAuditApplyDTO checkAndConvertApplyValidity(CrmInformationManagementApplyFormDTO applyReq) {
        try {
            BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO =
                    businessLicenceCertificationV2Task.convertToBusinessLicenseAuditApplyDTO(applyReq);
            if (Objects.isNull(businessLicenseAuditApplyDTO) || Objects.isNull(businessLicenseAuditApplyDTO.getBusinessLicense())) {
                throw new ContractBizException("营业执照审批信息为空");
            }
            return businessLicenseAuditApplyDTO;
        } catch (Exception e) {
            throw new ContractBizException("营业执照审批信息提取失败");
        }
    }

    private void checkContractSuccess(String merchantSn) {
        boolean contractSuccess = merchantBasicInfoBiz.isContractSuccess(merchantSn);
        if (!contractSuccess) {
            throw new ContractBizException("尚未开通间连扫码");
        }
    }

    private void checkExistedLicenseUpdateTask(String merchantSn) {
        boolean existed = isExistedProcessingTask(merchantSn);
        if (existed) {
            throw new ContractBizException("商户已有未完成的营业执照更新任务");
        }
    }

    private String getInUseAcquirer(String merchantSn) {
        Optional<ContractStatusDO> contractStatusOpt = contractStatusDAO.getByMerchantSn(merchantSn);
        if (!contractStatusOpt.isPresent() || !Objects.equals(contractStatusOpt.get().getStatus(), ContractStatus.STATUS_SUCCESS)) {
            throw new ContractBizException("商户尚未报备成功");
        }
        return contractStatusOpt.get().getAcquirer();
    }

    private void checkCanUpdateLicense(String merchantSn) {
        // 当前收单机构必须是三方
        String inUseAcquirer = getInUseAcquirer(merchantSn);
        McAcquirerDO acquirer = mcAcquirerDAO.getByAcquirer(inUseAcquirer);
        //中投作为银行,但本身是三方,可以做营业执照更新.
        if (AcquirerTypeEnum.UMB.getValue().equals(acquirer.getAcquirer())) {
            return;
        }
        if (!Objects.equals(acquirer.getType(), AcquirerOrgTypeEnum.THIRD_PARTY.getValue())) {
            throw new ContractBizException("当前所在收单机构不支持");
        }
    }

    private void checkCanMicroUpgrade(String merchantSn) {
        String inUseAcquirer = getInUseAcquirer(merchantSn);
        Tuple2<List<String>, String> reContractAcquirers = businessLicenceCertificationV2Task.listNeedToReContractAcquirers(merchantSn, inUseAcquirer);
        if (CollectionUtils.isEmpty(reContractAcquirers.get_1())) {
            throw new ContractBizException("营业执照认证，获取需要重新进件的收单机构为空，" + reContractAcquirers.get_2());
        }
    }

    private void checkCanUpdateBusinessName(CrmInformationManagementApplyFormDTO applyReq, MerchantInfo merchantInfo) {
        BusinessLicenseAuditApplyDTO businessLicenseAuditApplyDTO;
        try {
            businessLicenseAuditApplyDTO = businessLicenceCertificationV2Task.convertToBusinessLicenseAuditApplyDTO(applyReq);
        } catch (Exception e) {
            throw new ContractBizException("审批单信息提取失败");
        }
        boolean changeBusinessName = StringUtils.isNotBlank(businessLicenseAuditApplyDTO.getBusinessLicense().getBusinessName()) &&
                !StringUtils.equals(merchantInfo.getBusiness_name(), businessLicenseAuditApplyDTO.getBusinessLicense().getBusinessName());
        // 经营名称变更，需要30天校验
        if (changeBusinessName) {
            long end = System.currentTimeMillis();
            long start = end - ChronoUnit.DAYS.getDuration().toMillis() * 30;
            ListResult infos = commonFieldService.findFieldAppInfos(
                    new PageInfo(1, 1, start, end),
                    CollectionUtil.hashMap(
                            "field_type", "business_name",
                            "merchant_id", merchantInfo.getId(),
                            "status", 3
                    )
            );
            if (WosaiCollectionUtils.isNotEmpty(infos.getRecords())
                    || isLicenseUpdateExistSuccessBusinessNameChange(merchantInfo.getSn())) {
                throw new ContractBizException("商户经营名称30天内只能只能修改一次");
            }
        }
        // 校验是否有进行中的经营名称变更
        GetFieldAppStatusReq getFieldAppStatusReq = new GetFieldAppStatusReq();
        getFieldAppStatusReq.setMerchantId(merchantInfo.getId());
        getFieldAppStatusReq.setFieldType("business_name");
        GetFieldAppStatusResp fieldAppStatus = commonFieldService.getFieldAppStatus(getFieldAppStatusReq);
        if (Objects.nonNull(fieldAppStatus) && (
                Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_INIT) ||
                Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_PENDING) ||
                Objects.equals(fieldAppStatus.getFieldAppStatus(), CrmApplyConstant.STATUS_FAIL)) ) {
            throw new ContractBizException("您的商户经营名称正在审核中，待经营名称审核通过后再进行证照变更");
        }
        // 高校食堂校验
        if (changeBusinessName) {
            PayWayActivityMainProcessStatusEnum statusEnum = paywayActivityService.queryWechatMainProcessMerchantStatus(merchantInfo.getSn());
            if (PayWayActivityMainProcessStatusEnum.IN_PROGRESS == statusEnum
                    || PayWayActivityMainProcessStatusEnum.APPLY_SUCCESS == statusEnum) {
                throw new ContractBizException("已报名微信高校活动，暂时无法修改商户经营名称");
            }
        }
    }


    @Override
    public Boolean isLicenseUpdateExistSuccessBusinessNameChange(String merchantSn) {
        Optional<InternalScheduleMainTaskDO> any = internalScheduleMainTaskDAO
                .listBySnAndType(merchantSn, InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V2.getValue())
                .stream().filter(mainTaskDO -> {
                    if (!Objects.equals(mainTaskDO.getStatus(), InternalScheduleMainTaskStatusEnum.PROCESS_SUCCESS.getValue())) {
                        return false;
                    }
                    if (mainTaskDO.getMtime().getTime() < System.currentTimeMillis() - ChronoUnit.DAYS.getDuration().toMillis() * 30) {
                        return false;
                    }
                    BusinessLicenseCertificationV2MainTaskContext mainTaskContextBOInner = JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV2MainTaskContext.class);
                    if (Objects.nonNull(mainTaskContextBOInner)) {
                        return mainTaskContextBOInner.getNeedUpdateBusinessName();
                    }
                    return false;
                }).findAny();
        return any.isPresent();
    }

    /**
     * 营业执照处理v2
     * 新增营业执照审批单处理任务
     *
     * @param applyReq 营业执照审批单
     * @return 处理结果
     */
    @Override
    public SubmitResultForCrmInfoManageDTO submitBusinessLicenseApplyToContractSideAudit(CrmInformationManagementApplyFormDTO applyReq) {
        CheckResultForCrmInfoManageDTO checkResultForCrmInfoManageDTO = checkSubmitBusinessLicenseApplyToContractSideAudit(applyReq);
        if (!checkResultForCrmInfoManageDTO.checkPass()) {
            noticeCrmSubmitResultFail(applyReq.getFieldAppInfoId(), applyReq.getMerchantId(), checkResultForCrmInfoManageDTO.getSubmitCheckMsg());
            return SubmitResultForCrmInfoManageDTO.fail(checkResultForCrmInfoManageDTO.getSubmitCheckMsg());
        }
        try {
            //V3 AT一致性
            if(businessLicenceCertificationV3Task.isMicroUpgradeV3(merchantBasicInfoBiz.getMerchantInfoById(applyReq.getMerchantId()).getSn())) {
                return businessLicenceCertificationV3Task.insertTask(applyReq);
            }
            return businessLicenceCertificationV2Task.insertTask(applyReq);
        } catch (Exception e) {
            log.error("新增营业执照审批单处理任务失败, 商户号:{}", applyReq.getMerchantId(), e);
            noticeCrmSubmitResultFail(applyReq.getFieldAppInfoId(), applyReq.getMerchantId(), e.getMessage());
            return SubmitResultForCrmInfoManageDTO.fail(e.getMessage());
        }
    }

    /**
     * 已经和crm沟通，即使submitBusinessLicenseApplyToContractSideAudit返回失败，审批单失败了，crm也没法触发发事件消息
     * 所以这里手动再触发。。。。。。。。。。。。。
     */
    private void noticeCrmSubmitResultFail(Integer fieldAppInfoId, String merchantId, String failMsg) {
        try {
            String failMsgMap = errorMsgPromptConvertor.getUpgradeCheckFailPromptMessageMapJson(failMsg, LicenseUpgradeErrorMsgPromptConvertor.DEFAULT_MESSAGE);
            crmLicenseApplyUpdate.updateCrmLicenseApplyStatus(fieldAppInfoId, CrmApplyFormAuditStatusEnum.AUDIT_FAIL.getValue(), failMsgMap);
        } catch (Exception e) {
            log.error("通知crm营业执照审批单失败, merchantId:{}, fieldAppInfoId:{}, failMsg:{}", merchantId, fieldAppInfoId, failMsg, e);
        }
    }


    /**
     * 营业执照更新-crm 用户验证金额
     *
     * @param req req
     * @return 验证结果
     */
    @Override
    public LicenseUpdateAmountVerifyResDTO verifyAmountForCrmUpdateLicense(ApiRequestParam<LicenseUpdateAmountVerifyReqDTO> req) {
        String merchantId = req.getBodyParams().getMerchantId();
        BigDecimal amount = req.getBodyParams().getAmount();
        String businessId = req.getBodyParams().getBusinessId();
        Integer fieldAppInfoId = req.getBodyParams().getFieldAppInfoId();
        return doVerifyAmountForUpdateLicense(merchantId, amount, businessId, fieldAppInfoId);
    }

    private LicenseUpdateAmountVerifyResDTO doVerifyAmountForUpdateLicense(String merchantId, BigDecimal amount, String businessId, Integer fieldAppInfoId) {
        AmountVerify amountVerify = new AmountVerify();
        amountVerify.setAmount(amount);
        amountVerify.setBusiness_id(businessId);
        amountVerify.setPlat_form("merchant-contract-job");
        VerifyResp verifyResp = accountVerifyService.verifyAmount(amountVerify);
        if (verifyResp.getRetried() > verifyResp.getMax_retry()) {
            // crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(fieldAppInfoId, new BankAccountVerifyTaskHandler.AccountVerifyRecord(businessId, BankAccountVerifyTaskHandler.STATUS_VERIFY_FAIL));
            return LicenseUpdateAmountVerifyResDTO.exceedMaximumNumber();
        }
        if (verifyResp.getValid()) {
            try {
                crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(fieldAppInfoId, new BankAccountVerifyTaskHandler.AccountVerifyRecord(businessId, BankAccountVerifyTaskHandler.STATUS_VERIFY_SUCCESS));
            } catch (Exception e) {
                log.error("更新商户营业执照审批单状态失败, fieldAppInfoId:{}, businessId:{}", fieldAppInfoId, businessId, e);
                return LicenseUpdateAmountVerifyResDTO.verifyFailed(e.getMessage(), verifyResp.getRetried(), verifyResp.getMax_retry());
            }
            return LicenseUpdateAmountVerifyResDTO.verifySuccess(verifyResp.getRetried(), verifyResp.getMax_retry());
        }
        if (verifyResp.getRetried() == verifyResp.getMax_retry()) {
            crmLicenseApplyUpdate.syncCrmLicenseApplyAccountVerifyStatus(fieldAppInfoId, new BankAccountVerifyTaskHandler.AccountVerifyRecord(businessId, BankAccountVerifyTaskHandler.STATUS_VERIFY_FAIL));
            merchantBasicInfoBiz.getMerchantSnByMerchantId(merchantId)
                    .ifPresent(t -> interScheduleTaskService.scheduleProcessingTask(t, InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V2));
            return LicenseUpdateAmountVerifyResDTO.verifyFailed(verifyResp.getMsg(), verifyResp.getRetried(), verifyResp.getMax_retry());
        }
        return LicenseUpdateAmountVerifyResDTO.verifyFailed(verifyResp.getMsg(), verifyResp.getRetried(), verifyResp.getMax_retry());
    }


    /**
     * 营业执照更新-app 用户验证金额
     * 需要通知进件侧，从而更新审批单
     *
     * @param verifyReqDTO req
     * @return 验证结果
     */
    @Override
    public LicenseUpdateAmountVerifyResDTO verifyAmountForAppUpdateLicense(LicenseUpdateAmountVerifyReqDTO verifyReqDTO) {
        return doVerifyAmountForUpdateLicense(verifyReqDTO.getMerchantId(), verifyReqDTO.getAmount(), verifyReqDTO.getBusinessId(), verifyReqDTO.getFieldAppInfoId());
    }

    @Resource
    private UpdateBusinessNameTaskHandler updateBusinessNameTaskHandler;

    /**
     * 营业执照变更-修改经营名称
     * 此接口调用时机在营业执照变更已经完成
     *
     * @param merchantId   商户号
     * @param businessName 经营名称
     */
    @Override
    public void updateBusinessNameInLicenseCertificate(String merchantId, String businessName) {
        updateBusinessNameTaskHandler.handleUpdateBusinessNameUpdate(merchantId, businessName);
    }

    /**
     * 小微升级已删除参数，更新禁用原因
     *
     * @param merchantSn 商户号
     */
    @Override
    public void updateDisableReasonForMicroUpgradeDeletedParams(String merchantSn) {
        List<MerchantProviderParamsDO> merchantProviderParamsDOS = merchantProviderParamsDAO.listDeletedParamsByMerchantSn(merchantSn);
        if (CollectionUtils.isEmpty(merchantProviderParamsDOS)) {
            return;
        }
        List<MerchantProviderParamsDO> paramsToUpdate = merchantProviderParamsDOS.stream()
                .filter(t -> Objects.equals(t.getPayway(), PaywayEnum.ACQUIRER.getValue()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(paramsToUpdate)) {
            return;
        }
        for (MerchantProviderParamsDO paramsDO : paramsToUpdate) {
            if (StringUtils.isNotBlank(paramsDO.getExtra())) {
                List<Object> disableReasons = merchantTradeParamsBiz.getDisableReasons(paramsDO.getExtra().getBytes(StandardCharsets.UTF_8));
                Optional<Object> any = disableReasons.stream().filter(disableReason -> {
                    JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(disableReason));
                    return StringUtils.equals(MapUtils.getString(jsonObject, MerchantProviderParamsDO.DISABLE_REASON_KEY),
                            BusinessLicenceCertificationTask.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON);
                }).findAny();
                if (any.isPresent()) {
                    log.warn("交易参数已被禁用,商户号:{}, 收单机构商户号:{}, 禁用原因:{}", merchantSn, paramsDO.getProviderMerchantId(), JSON.toJSONString(any.get()));
                    continue;
                }
            }
            MerchantTradeParamsDisableReasonManageReqDTO req = new MerchantTradeParamsDisableReasonManageReqDTO();
            req.setMerchantSn(merchantSn);
            req.setOperateType(TradeParamsDisableReasonOperateTypeEnum.ADD);
            req.setAccessSide(TradeParamsDisableReasonAccessSideEnum.CUA);
            req.setOperator("merchant-contract-job");
            req.setDisableReason(BusinessLicenceCertificationTask.MICRO_UPGRADE_ACQUIRER_MERCHANT_DISABLE_REASON);
            req.setDisableReasonDetail("批量禁用小微升级已删除的收单机构商户号");
            try {
                merchantTradeParamsBiz.disableAcquirerMerchantId(paramsDO.getProviderMerchantId(), req);
            } catch (Exception e) {
                log.error("updateDisableReasonForMicroUpgradeDeletedParams error, merchantSn:{}, paramsDO:{}", merchantSn, JSON.toJSONString(paramsDO), e);
            }
        }
    }



    /**
     * 是否为V3版本小微升级
     * @param merchantSn 商户号
     * @return
     */
    @Override
    public Boolean isMicroUpgradeV3(String merchantSn) {
        return Objects.equals(determineMicroUpgradeVersion(new MicroUpgradeDTO().setMerchantSn(merchantSn)), UpgradeVersion.V3);
    }

    @Autowired
    private ApplicationApolloConfig applicationApolloConfig;

    /**
     * 判断当前小微升级应该选择的升级版本
     *
     * @param mcUpgradeDTO 小微升级版本选择-入参
     * @return UpgradeVersion
     */
    public UpgradeVersion determineMicroUpgradeVersion(MicroUpgradeDTO mcUpgradeDTO) {
        final String merchantSn = mcUpgradeDTO.getMerchantSn();
        if(!merchantBasicInfoBiz.isMerchantLicenseMicro(merchantSn)) {
            return UpgradeVersion.V2;
        }
        ApplicationApolloConfig.MicroUpgradeV3RuleConfig ruleConfig = applicationApolloConfig
                .getMicroUpgradeV3RuleConfig();
        if (ruleConfig == null || ruleConfig.mode == null) {
            log.warn("micro_upgrade_v3_rule配置缺失或mode为空，商户:{}，默认返回V2", merchantSn);
            return UpgradeVersion.V2;
        }
        String mode = ruleConfig.mode.trim().toLowerCase();
        switch (mode) {
            case "all":
                log.info("micro_upgrade_v3_rule模式: all，商户:{} 命中V3", merchantSn);
                return UpgradeVersion.V3;
            case "none":
                log.info("micro_upgrade_v3_rule模式: none，商户:{} 命中V2", merchantSn);
                return UpgradeVersion.V2;
            case "whitelist":
                if (ruleConfig.whitelist != null && ruleConfig.whitelist.contains(merchantSn)) {
                    log.info("micro_upgrade_v3_rule模式: whitelist，商户:{} 命中V3", merchantSn);
                    return UpgradeVersion.V3;
                }
                log.info("micro_upgrade_v3_rule模式: whitelist，商户:{} 命中V2", merchantSn);
                return UpgradeVersion.V2;
            case "hash":
                if (ruleConfig.hashMod != null && ruleConfig.hashHit != null) {
                    int mod = ruleConfig.hashMod;
                    int hit = Math.abs(merchantSn.hashCode()) % mod;
                    if (ruleConfig.hashHit.contains(hit)) {
                        log.info("micro_upgrade_v3_rule模式: hash，商户:{} 命中V3，hash值:{}", merchantSn, hit);
                        return UpgradeVersion.V3;
                    }
                }
                log.info("micro_upgrade_v3_rule模式: hash，商户:{} 命中V2", merchantSn);
                return UpgradeVersion.V2;
            case "whitelist+hash":
                if (ruleConfig.whitelist != null && ruleConfig.whitelist.contains(merchantSn)) {
                    log.info("micro_upgrade_v3_rule模式: whitelist+hash，商户:{} 白名单命中V3", merchantSn);
                    return UpgradeVersion.V3;
                }
                if (ruleConfig.hashMod != null && ruleConfig.hashHit != null) {
                    int mod = ruleConfig.hashMod;
                    int hit = Math.abs(merchantSn.hashCode()) % mod;
                    log.info("micro_upgrade_v3_rule模式: whitelist+hash，商户:{} hash值:{}", merchantSn, hit);
                    if (ruleConfig.hashHit.contains(hit)) {
                        log.info("micro_upgrade_v3_rule模式: whitelist+hash，商户:{} hash命中V3，hash值:{}", merchantSn, hit);
                        return UpgradeVersion.V3;
                    }
                }
                log.info("micro_upgrade_v3_rule模式: whitelist+hash，商户:{} 命中V2", merchantSn);
                return UpgradeVersion.V2;
            default:
                log.warn("micro_upgrade_v3_rule模式: {} 未知，商户:{}，默认返回V2", mode, merchantSn);
                return UpgradeVersion.V2;
        }
    }

    @Resource
    private AcquirerFacade acquirerFacade;

    @Override
    public MerchantAcquireInfoBO getMerchantAcquireInfoBO(String merchantSn) {
        AtomicReference<MerchantAcquireInfoBO> merchantAcquireInfoBO = new AtomicReference<>();
        List<InternalScheduleMainTaskDO> mainTaskDOList = internalScheduleMainTaskDAO.listBySnAndTypes(merchantSn, Lists.newArrayList(InternalScheduleTaskTypeEnum.BUSINESS_LICENCE_CERTIFICATION_V3.getValue()));
        if (mainTaskDOList != null) {
            final Optional<InternalScheduleMainTaskDO> mainTaskDO = mainTaskDOList.stream().filter(InternalScheduleMainTaskDO::taskInProcessing)
                    .findFirst();
            if(mainTaskDO.isPresent()) {
                final BusinessLicenseCertificationV3MainTaskContext businessLicenseCertificationV3MainTaskContext = JSON.parseObject(mainTaskDO.get().getContext(),
                        BusinessLicenseCertificationV3MainTaskContext.class);
                final Long mainContractTaskId = businessLicenseCertificationV3MainTaskContext.getMainContractTaskId();
                final String acquirer = mainTaskDO.get().getAcquirer();
                acquirerFacade.getSharedAbilityByAcquirer(acquirer).ifPresent(sharedAbility -> {
                    merchantAcquireInfoBO.set(sharedAbility.getAcquireInfoFromContractSubTask(mainContractTaskId));
                    log.info("getMerchantAcquireInfoBO, merchantSn:{}, acquirer:{}, merchantAcquireInfoBO:{}", merchantSn, acquirer, JSON.toJSONString(merchantAcquireInfoBO));
                });

            }
        }
        return merchantAcquireInfoBO.get();
    }

    @Override
    public String changeParamsTime(String merchantSn) {
        return "";
    }



}
