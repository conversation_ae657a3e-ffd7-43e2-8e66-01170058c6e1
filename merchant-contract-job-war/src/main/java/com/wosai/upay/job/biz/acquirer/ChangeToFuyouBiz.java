package com.wosai.upay.job.biz.acquirer;

import com.shouqianba.cua.enums.contract.ContractTaskTypeEnum;
import com.shouqianba.cua.enums.contract.ProviderEnum;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.enums.status.PayMchAuthStatusEnum;
import com.wosai.common.exception.CommonPubBizException;
import com.wosai.common.utils.WosaiCollectionUtils;
import com.wosai.common.utils.WosaiDateTimeUtils;
import com.wosai.trade.service.activity.constant.ActivityConstants;
import com.wosai.trade.service.activity.response.ApplyConditionQueryResponse;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.constant.ContractRuleConstants;
import com.wosai.upay.job.externalservice.trademanage.TradeManageClient;
import com.wosai.upay.job.model.DO.McAcquirerChange;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.refactor.dao.ContractTaskDAO;
import com.wosai.upay.job.refactor.model.entity.ContractTaskDO;
import com.wosai.upay.merchant.contract.exception.ContractBizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 收单机构切换到海科
 *
 * <AUTHOR>
 * @date 2024-01-04
 */
@Component("fuyou-AcquirerChangeBiz")
@Slf4j
public class ChangeToFuyouBiz extends AbstractIndirectAcquirerChangeBiz {

    @Autowired
    private TradeManageClient tradeManageClient;

    @Autowired
    private ContractTaskDAO contractTaskDAO;


    @Override
    public int getProviderCode(String acquirer) {
        return ProviderEnum.PROVIDER_FUYOU.getValue();
    }

    @Override
    public String getContractGroup(String merchantSn) {
        return ContractRuleConstants.CHANGE_TO_FUYOU_RULE_GROUP;
    }

    @Override
    protected void acquirerSpecialCheck(String merchantSn, String merchantId, String sourceAcquirer, String targetAcquirer, boolean immediately) {
        if (!immediately) {
            return;
        }

        McAcquirerChange tmp = new McAcquirerChange();
        tmp.setMerchant_sn(merchantSn);
        tmp.setTarget_acquirer(targetAcquirer);
        tmp.setSource_acquirer(sourceAcquirer);
        if (!hasContract(tmp)) {
            return;
        }

        // 检查商户当天有没有申请费变更
        Optional<ApplyConditionQueryResponse> applyResponse = tradeManageClient.getLatestApply(merchantSn, ActivityConstants.APPLY_ING);
        boolean applyFee = applyResponse.isPresent()
                && applyResponse.get().getCreateTime() > WosaiDateTimeUtils.getDayStart(System.currentTimeMillis());
        if (applyFee) {
            throw new ContractBizException("商户当天存在费率活动申请，不支持实时切换到富友");
        }

        applyResponse = tradeManageClient.getLatestApply(merchantSn, ActivityConstants.EFFECT);
        applyFee = applyResponse.isPresent()
                && applyResponse.get().getCreateTime() > WosaiDateTimeUtils.getDayStart(System.currentTimeMillis());
        if (applyFee) {
            throw new ContractBizException("商户当天存在费率活动申请，不支持实时切换到富友");
        }

        // 检查有没有同步费率任务
        Optional<ContractTaskDO> feeRateTask = contractTaskDAO.getLastedTaskBySnAndType(merchantSn, ContractTaskTypeEnum.UPDATE_MERCHANT_RATE.getValue());
        if (feeRateTask.isPresent() && feeRateTask.get().getCreateAt().getTime() > WosaiDateTimeUtils.getDayStart(System.currentTimeMillis())) {
            throw new ContractBizException("商户当天同步过费率，不支持实时切换到富友");
        }
    }

    @Override
    protected void updateClearanceProvider(McAcquirerChange change) {
        tradeConfigService.updateClearanceProvider(change.getMerchant_id(), TransactionParam.CLEARANCE_PROVIDER_FUYOU);
    }


    @Override
    protected List<MerchantProviderParams> getDefaultChangeParams(McAcquirerChange change) {
        //云闪付、翼支付,京东钱包交易参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayNotIn(Arrays.asList(PaywayEnum.ACQUIRER.getValue(), PaywayEnum.ALIPAY.getValue(), PaywayEnum.WEIXIN.getValue()))
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime asc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        //支付宝交易参数
        params.add(getAliParams(change));
        params.add(getWxParams(change));
        return params;
    }


    /**
     * 获取支付宝参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getAliParams(McAcquirerChange change) {
        //获取最新的支付宝参数
        MerchantProviderParamsExample example = new MerchantProviderParamsExample();
        example.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.ALIPAY.getValue())
                .andDeletedEqualTo(false);
        example.setOrderByClause("ctime desc");
        List<MerchantProviderParams> params = paramsMapper.selectByExampleWithBLOBs(example);
        if (WosaiCollectionUtils.isNotEmpty(params)) {
            return params.get(0);
        }
        throw new CommonPubBizException(String.format("商户号:%s,没有找到支付宝参数", change.getMerchant_sn()));
    }

    /**
     * 获取微信参数
     *
     * @param change
     * @return
     */
    private MerchantProviderParams getWxParams(McAcquirerChange change) {
        // 先获取新渠道的微信参数
        MerchantProviderParamsExample wxExample = new MerchantProviderParamsExample();
        wxExample.or().andMerchant_snEqualTo(change.getMerchant_sn())
                .andProviderEqualTo(getProviderCode(change.getTarget_acquirer()))
                .andPaywayEqualTo(PaywayEnum.WEIXIN.getValue())
                .andDeletedEqualTo(false);
        wxExample.setOrderByClause("ctime desc");
        List<MerchantProviderParams> wxParams = paramsMapper.selectByExampleWithBLOBs(wxExample);
        if (WosaiCollectionUtils.isEmpty(wxParams)) {
            throw new ContractBizException("缺少可用微信子商户号");
        }

        // 有多个微信交易参数优先获取实名过的子商户号，都未实名则获取最新报备的一个
        for (MerchantProviderParams wxParam : wxParams) {
            if (PayMchAuthStatusEnum.YES.getValue().equals(wxParam.getAuth_status())) {
                return wxParam;
            }
        }
        return wxParams.get(0);
    }


    @Override
    protected void sourceAcquirerPostBiz(McAcquirerChange change) {
        super.sourceAcquirerPostBiz(change);
        super.invalidJdPay(change.getMerchant_id(), change.getTarget_acquirer(),change.getMerchant_sn());
    }
}
