package com.wosai.upay.job.xxljob.template;

import com.shouqianba.cua.utils.thread.ThreadPoolWorker;
import com.wosai.middleware.hera.toolkit.trace.RunnableWrapper;
import com.wosai.upay.job.ShutdownSignal;
import com.wosai.upay.job.xxljob.model.BatchJobParam;
import com.wosai.upay.job.xxljob.threadpool.JobThreadPoolExecutor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.wosai.upay.job.xxljob.model.BatchExecTypeEnum.*;

@Slf4j
@Service
public abstract class AbstractBatchJobHandler<T> implements BatchJobHandler {
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    @Qualifier("batchJobHandlerThreadPool")
    private JobThreadPoolExecutor batchJobHandlerThreadPool;

    @Override
    public void handle(BatchJobParam param) {
        if (SYNC_SERIAL.equals(param.getExecType())) {
            List<T> dataList = queryTaskItems(param);
            dataList.forEach(this::doHandle);
        } else if (SYNC_PARALLEL.equals(param.getExecType())) {
            List<T> dataList = queryTaskItems(param);
            ThreadPoolWorker<Object> threadPoolWorker = ThreadPoolWorker.of();
            for (T data : dataList) {
                threadPoolWorker.addWork(() -> doHandle(data));
            }
            threadPoolWorker.doWorks();
        } else if (ASYNC_SERIAL.equals(param.getExecType())) {
            getExecutor().submit(RunnableWrapper.of(() -> {
                queryTaskItems(param).forEach(this::doHandle);
            }));
        } else if (ASYNC_PARALLEL.equals(param.getExecType())) {
            getExecutor().submit(RunnableWrapper.of(() -> {
                ThreadPoolWorker<Object> threadPoolWorker = ThreadPoolWorker.of();
                queryTaskItems(param).forEach(r -> threadPoolWorker.submit(() -> doHandle(r)));
            }));
        } else {
            throw new IllegalArgumentException("执行模式不正确");
        }
    }

    protected void doHandle(T data) {
        if (ShutdownSignal.isShuttingDown()) {
            return;
        }
        RLock lock = redissonClient.getLock(getLockKey(data));
        try {
            if (lock.tryLock()) {
                // 这里可能会出现重复数据，需要在方法内部重新判断是否需要处理
                doHandleSingleData(data);
            }
        } catch (Exception e) {
            log.error("{} batch task execute error", this.getClass().getSimpleName(), e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    protected JobThreadPoolExecutor getExecutor() {
        return batchJobHandlerThreadPool;
    }

    public abstract List<T> queryTaskItems(BatchJobParam param);

    public abstract String getLockKey(T t);

    public abstract void doHandleSingleData(T t);
}
