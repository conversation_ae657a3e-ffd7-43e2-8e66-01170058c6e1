package com.wosai.upay.job.refactor.task.license.micro;

import com.alibaba.fastjson.JSON;
import com.shouqianba.cua.enums.contract.*;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.wosai.mc.model.MerchantInfo;
import com.wosai.profit.sharing.model.request.OpenFuYouSharingReq;
import com.wosai.profit.sharing.service.SharingOpenService;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.job.Constants.McConstant;
import com.wosai.upay.job.adapter.apollo.ApplicationApolloConfig;
import com.wosai.upay.job.biz.IndustryMappingCommonBiz;
import com.wosai.upay.job.biz.WechatAuthBiz;
import com.wosai.upay.job.mapper.MerchantProviderParamsMapper;
import com.wosai.upay.job.model.DO.MerchantProviderParams;
import com.wosai.upay.job.model.DO.MerchantProviderParamsExample;
import com.wosai.upay.job.providers.FuyouProvider;
import com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouAcquirerFacade;
import com.wosai.upay.job.refactor.dao.ContractSubTaskDAO;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.ContractSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV3MainTaskContext;
import com.wosai.upay.job.refactor.utils.BeanCopyUtils;
import com.wosai.upay.job.refactor.task.license.micro.builder.FuyouTradeParamsBuilder;
import com.wosai.upay.job.service.TaskResultServiceImpl;
import com.wosai.upay.job.util.ChatBotUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;

import static com.wosai.upay.job.refactor.biz.acquirer.fuyou.FuYouContractProcessor.FU_YOU_CONTRACT_SETTLE_TYPE_KEY;

/**
 * 富友营业执照认证重新入网成功后参数处理
 *
 * <AUTHOR>
 * @date 2025/07/02 16:24
 */
@Component
@Slf4j
public class FuyouUpdateTradeParams extends AbstractUpdateTradeParamsTemplate {



    @Resource
    private FuyouProvider fuyouProvider;


    @Override
    public String getAcquirer() {
        return AcquirerTypeEnum.FU_YOU.getValue();
    }

    @Override
    Integer getProvider() {
        return ProviderEnum.PROVIDER_FUYOU.getValue();
    }

    private final BlockingQueue<MerchantProviderParamsDO> retrySyncToHaikePayMerchantIds = new LinkedBlockingQueue<>(50000);

    @Override
    void rollbackTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
    }

    @Override
    public String getContractTermNo(String merchantSn) {
        // 暂时用不到
        return "";
    }

    @Override
    public String getMerchantConfigProviderMerchantIdKey() {
        return "provider_mch_id";
    }

    @Override
    public void updateClearanceProviderWhenSameAcquirer(String merchantId) {
        coreBTradeConfigService.updateMerchantSwitchMchTime(merchantId, TransactionParam.CLEARANCE_PROVIDER_FUYOU, System.currentTimeMillis());
        tradeConfigService.updateClearanceProvider(merchantId, TransactionParam.CLEARANCE_PROVIDER_FUYOU);
    }

    @Override
    void asyncExistedStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {

    }

    @Override
    void asyncExistedStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
    }

    @Override
    void acquirerSpecialProcess(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        // nothing todo yet
    }




    @Resource
    private TaskResultServiceImpl taskResultService;
    @Resource
    private ChatBotUtil chatBotUtil;
    @Resource
    private MerchantProviderParamsMapper merchantProviderParamsMapper;

    @Resource
    protected ApplicationApolloConfig apolloConfig;
    @Override
    void acquirerSpecialProcessV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        BusinessLicenceCertificationV3Task.SubTaskContextBOInner ctx = JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV3Task.SubTaskContextBOInner.class);
        final String merchantSn = subTaskDO.getMerchantSn();
        final Long contractTaskId = ctx.getContractTaskId();
        try{
            final Map<String, Object> contractContext = businessLicenceCertificationV3Task.buildContractContext(mainTaskDO);
            taskResultService.checkAndSendTodo(contractContext);
        }catch (Exception e){
            log.error("商户号:{},小微升级入网成功后发送富友补充对公凭证待办失败,{}", merchantSn,contractTaskId);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("发送富友补充对公凭证待办失败,商户SN:%s,终端号:%s,原因:%s", merchantSn, contractTaskId, "发送富友补充对公凭证待办失败"));
        }
        try {
            log.info("触发fuyou资金归集,{},{},{}", merchantSn, contractTaskId);
            MerchantProviderParamsExample example = new MerchantProviderParamsExample();
            example.or().andMerchant_snEqualTo(merchantSn)
                    .andProviderEqualTo(ProviderEnum.PROVIDER_FUYOU.getValue())
                    .andPaywayEqualTo(PaywayEnum.ACQUIRER.getValue())
                    .andDeletedEqualTo(false);
            example.setOrderByClause("ctime desc");
            List<MerchantProviderParams> merchantProviderParams = merchantProviderParamsMapper.selectByExampleWithBLOBs(example);
            if (merchantProviderParams != null && merchantProviderParams.size() > 0) {
                OpenFuYouSharingReq req = new OpenFuYouSharingReq();
                req.setPrivateMchId(merchantProviderParams.get(0).getProvider_merchant_id());
                String settleType = getContractSettleTypeByMerchantSn(merchantSn);

                if (!Objects.equals(FuYouContractSettleTypeEnum.MANUAL_SETTLEMENT_D1.getValue().toString(), settleType)) {
                    sharingOpenService.openFuYouSharing(req);
                }
            }
        } catch (Exception e) {
            log.error("商户号:{},触发fuyou资金归集异常,{}", merchantSn,contractTaskId);
            chatBotUtil.sendMessageToContractWarnChatBot(String.format("触发富友资金归集异常,商户SN:%s,任务id:%s,原因:%s", merchantSn, contractTaskId, "触发富友资金归集失败"));
        }
    }


    @Resource
    private ContractSubTaskDAO contractSubTaskDAO;

    @Resource
    private SharingOpenService sharingOpenService;

    /**
     * 从富友的进件报文中，获取结算类型
     * 获取不到从进件的阿波罗配置中获取默认值
     *
     * @param merchantSn 商户号
     * @return 结算类型
     */
    public String  getContractSettleTypeByMerchantSn(String merchantSn) {
        try {
            List<ContractSubTaskDO> contractSubTaskDOS = contractSubTaskDAO.listContractSubTaskDOs(merchantSn, McConstant.RULE_GROUP_FUYOU,
                    ContractSubTaskTypeEnum.MERCHANT_OPENING.getValue(),
                    ContractSubTaskProcessStatusEnum.PROCESS_SUCCESS.getValue());
            Optional<ContractSubTaskDO> subTaskDOOptional = contractSubTaskDOS.stream()
                    .filter(subTaskDO -> Objects.equals(subTaskDO.getPayway(), PaywayEnum.ACQUIRER.getValue())
                            && Objects.equals(subTaskDO.getContractRule(), AcquirerTypeEnum.FU_YOU.getValue()))
                    .sorted(Comparator.comparing(ContractSubTaskDO::getCreateAt).reversed())
                    .findFirst();
            String requestBody = subTaskDOOptional.map(ContractSubTaskDO::getRequestBody).orElse("");
            String settleTypeFromRequest = MapUtils.getString(JSON.parseObject(requestBody), FU_YOU_CONTRACT_SETTLE_TYPE_KEY);
            if (StringUtils.isNotBlank(settleTypeFromRequest)) {
                return settleTypeFromRequest;
            }
        } catch (Exception e) {
            log.warn("解析富友进件报文获取结算类型失败, 商户号: {}", merchantSn, e);
        }
        return apolloConfig.getFuYouDefaultContractSettleType();
    }

    @Override
    void addMerchantLevelStoreAndTerminalBind(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO,
                                              MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams) {

    }


    @Override
    void addMerchantLevelStoreAndTerminalBindV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO, MerchantInfo merchantInfo, List<MerchantProviderParamsDO> newParams) {
        fuyouProvider.doCreateProviderTerminal(mainTaskDO.getMerchantSn(), ProviderEnum.PROVIDER_FUYOU.getValue());
    }



    @Override
    void updateTradeParamsForPayIsNull(MerchantInfo merchantInfo, InternalScheduleSubTaskDO subTaskDO) {

    }





    @Override
    Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMap(InternalScheduleSubTaskDO subTaskDO) {
        return Collections.emptyMap();
    }




    @Resource
    @Lazy
    private BusinessLicenceCertificationV3Task businessLicenceCertificationV3Task;


    @Autowired
    private FuyouTradeParamsBuilder fuyouTradeParamsBuilder;

    @Override
    Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMapV3(InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        return fuyouTradeParamsBuilder.buildNewNeedInsertParamsAndReturnOldNewMapV3(mainTaskDO, subTaskDO);
    }
}
