package com.wosai.upay.job.mapper;

import com.wosai.upay.job.interceptor.DaoLog;
import com.wosai.upay.job.model.ContractSubTask;
import com.wosai.upay.job.model.ContractSubTaskReq;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public interface ContractSubTaskMapper {
    int insert(ContractSubTask record);

    ContractSubTask selectByPrimaryKey(Long id);

    int updateByPrimaryKey(ContractSubTask record);

    @DaoLog
    int restartHxSubTask(@Param("p_task_id") Long pTaskId);

    List<ContractSubTask> selectByMerchatSn(String merchantSn);

    List<ContractSubTask> selectByMerchantSnAndParentIdAndStatusAndScheduleStatus(@Param("merchant_sn") String merchantSn, @Param("id") Long id, @Param("status") int status, @Param("schedule_status") int scheduleStatus);

    ContractSubTask getSubtasksByPtaskIdAndChannel(@Param("p_task_id") Long p_task_id, @Param("channel") String channel);

    List<ContractSubTask> getSubTasksByMerchantSnAndChannel(@Param("merchant_sn")String merchant_sn, @Param("channel") String channel);

    List<ContractSubTask> getSubtasksByMerchantAndPayway(@Param("merchant_sn") String merchant_sn, @Param("p_task_id") Long p_task_id);

    ContractSubTask selectByContractId(String contractId);

    List<ContractSubTask> selectByPTaskIdAndStatus(@Param("p_task_id") Long p_task_id, @Param("status") int status);

    List<ContractSubTask> selectByPTaskId(@Param("p_task_id") Long p_task_id);

    List<ContractSubTask> selectByDependTaskId(Long id);

    List<ContractSubTask> selectByATDependTaskId(Long id);

    // TODO: 2021/9/1 待定的功能 
    List<ContractSubTask> selectShopAndTermSubTaskByDependTaskId(Long id);

    // TODO: 2021/9/1 待定的功能 
    int updateShopTermSubTask(Long id, Long scheduleDependId);

    List<ContractSubTask> selectByDepenTaskIdAndScheduleStatus(long dependId, int scheduleStatus);

    List<ContractSubTask> selectByChannel(String channel);

    ContractSubTask selectLKLTaskByPTaskId(Long p_task_id);

    // TODO: 2021/9/1 待定的功能 
    ContractSubTask selectLKLV3TaskByPTaskId(Long p_task_id);

    List<ContractSubTask> selectLKLContractQueryTask(@Param("limit") Integer limit, @Param("create") String create);

    List<ContractSubTask> selectLKLV3ContractQueryTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);

    List<ContractSubTask> selectGuotongContractQueryTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);

    List<ContractSubTask> selectFuYouContractQueryTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);

    List<ContractSubTask> selectUnionMerchantContractResultQueryTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);

    /**
     * 获取入网任务的子任务 type=5
     * @param merchantSn 父任务id
     * @return 子任务列表
     */
    List<ContractSubTask> selectNetInSubTasksByMerchantSnAndRuleGroup(String merchantSn, String ruleGroup);

    List<ContractSubTask> selectNetInNoInfluenceSubTasksByPTaskId(Long p_task_id);

    /**
     * @param limit  每次查询次数
     * @param start 时间起点
     * @return List<ContractSubTask> 结果集
     * <AUTHOR>
     * @Description: 获取需要主动查询进件状态的
     * @time 14:44
     */
    List<ContractSubTask> selectPsbcContractQueryTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);


    /**
     * @param limit  每次查询次数
     * @param start 时间起点
     * @return List<ContractSubTask> 结果集
     * <AUTHOR>
     * @Description: 获取需要主动查询进件状态的
     * @time 14:44
     */
    List<ContractSubTask> selectPabContractQueryTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);


    /** 获取需要主动查询进件状态的泸州银行进件任务列表
     * @param limit  每次查询次数
     * @param start 时间起点
     * @return List<ContractSubTask> 结果集
     * <AUTHOR>
     * @time 13:47
     */
    List<ContractSubTask> selectLzbContractQueryTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);


    /**
     * @param limit  每次查询次数
     * @param start 时间起点
     * @return List<ContractSubTask> 结果集
     * <AUTHOR>
     * @Description: 获取需要主动查询进件状态的
     * @time 14:44
     */
    List<ContractSubTask> selectCcbContractQueryTask(@Param("limit") Integer limit,  @Param("start") String start, @Param("end") String end);

    /**
     * @param limit  每次查询次数
     * @param start 时间起点
     * @return List<ContractSubTask> 结果集
     * <AUTHOR>
     * @Description: 获取需要主动查询进件状态的
     * @time 14:44
     */
    List<ContractSubTask> selectFuyouUpdateQueryTask(@Param("start") String start, @Param("end") String end);


    List<ContractSubTask> selectByMerchantSnAndPTaskIdAndScheduleStatus(String merchantSn, long pTaskId, int scheduleStatus);

    List<ContractSubTask> selectByUpload(String create_at, Integer limit);

    List<ContractSubTask> selectByTemp(Long pTaskId);

    @Select("select * from contract_sub_task where merchant_sn = #{merchantSn} and payway = #{payway} limit 30")
    List<ContractSubTask> selectByMerchantSnAndPayway(@Param("merchantSn") String merchantSn, @Param("payway") Integer payway);

    /**
     * 更新contractSubTask结果
     */
    int setSubTaskResult(@Param("pre_status") int preStatus, @Param("result_status") int resultStatus, @Param("response_body") String responseBody, @Param("result") String result, @Param("id") Long id);

    /**
     * 更新contractSubTask status
     */
    int setSubTaskStatus(@Param("pre_status") int preStatus, @Param("result_status") int resultStatus, @Param("id") Long id);

    int setEnableScheduleByDepId(@Param("schedule_dep_task_id") Long id);

    List<ContractSubTask> queryUnionOpenTasks(String start, Integer limit);

    ContractSubTask getWeixinSub(@Param("taskId") Long takId);

    ContractSubTask getNearWeixinSub(@Param("merchantSn") String merchantSn);

    @Select("select * from contract_sub_task where p_task_id=#{pTaskId} and (payway is null or payway=0) and task_type!=99 and task_type != 14")
    List<ContractSubTask> getAcquireSubTask(@Param("pTaskId") Long pTaskId);

    /**
     * 根据商户号和支付方式获取相关子任务
     *
     * @param req
     * @return
     */
    List<ContractSubTask> findTasksByMerchantAndPayway(@Param("req") ContractSubTaskReq req);

    /**
     * 查询ums 进行中的任务
     * @param limit
     * @param create
     * @return
     */
    List<ContractSubTask> selectUmsContractQueryTask(@Param("limit") Integer limit, @Param("create") String create);


    List<ContractSubTask> findTaskByRecord(@Param("req") ContractSubTaskReq req);

    @Update("update contract_sub_task set status =#{status} where id =#{id} ")
    int updateStatusById(@Param("status") int status, @Param("id") Long id);

    /**
     * 查询到这个建行入网的子任务 可能有多条，要查最新的一个
     * @param merchantSn 商户号
     * @return 子任务
     */
    @Select("select * from contract_sub_task where merchant_sn=#{merchant_sn} and task_type = 5 and channel = 'ccb' and (payway is null or payway=0) order by create_at desc limit 1")
    ContractSubTask getCcbInsertSubTask(@Param("merchant_sn") String merchantSn);

    /**
     * 查询到平安签约的子任务 可能有多条，要查最新的一个
     * @param merchantSn 商户号
     * @return 子任务
     */
    @Select("select * from contract_sub_task where merchant_sn=#{merchant_sn} and task_type = 5 and contract_rule = 'pab-sign' and (payway is null or payway=0) order by create_at desc limit 1")
    ContractSubTask getPabSignSubTask(@Param("merchant_sn") String merchantSn);

    @Select("select * from contract_sub_task where priority>=#{start} and priority < now() and status in(0,1) and status_influ_p_task=0 and schedule_status=1 limit #{limit}")
    List<ContractSubTask> getNoInfluenceSubTask(@Param("limit") Integer limit, @Param("start") String start);

    @Select("select * from contract_sub_task where merchant_sn = #{merchant_sn} and contract_rule = #{contract_rule} and task_type = 14 and payway = 0 limit 1")
    ContractSubTask getProviderQuerySubTask(@Param("merchant_sn") String merchantSn, @Param ("contract_rule") String contractRule);

    @Select("select * from contract_sub_task where merchant_sn =#{merchantSn} and priority>=#{start} and status =10 and channel = 'tonglianV2' limit 10")
    List<ContractSubTask> selectMchTongLianV2UnCallbackTask(@Param("merchantSn") String merchantSn, @Param("start") String start);

    @Select("select * from contract_sub_task where priority>=#{start} and priority < #{end} and status =10 and channel = 'tonglianV2' limit #{limit}")
    List<ContractSubTask> getTongLianV2UnCallbackSubTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);

    @Select("select * from contract_sub_task where create_at >=#{start} and create_at < #{end} and status = 1 and channel = 'lzb' and payway = 0 and status_influ_p_task=1 limit #{limit}")
    List<ContractSubTask> getLuzhouUnCallbackSubTask(@Param("limit") Integer limit, @Param("start") String start, @Param("end") String end);

    @Select("select * from contract_sub_task where priority>=#{start} and priority < now() and status =99 and status_influ_p_task=0 and schedule_status=1 and task_type = 5 and channel = 'fuyou' limit #{limit}")
    List<ContractSubTask> getNoInfluenceSubTaskWaitCallBack(@Param("limit") Integer limit, @Param("start") String start);


    @Select("select * from contract_sub_task where merchant_sn = #{merchant_sn} and task_type = 11")
    List<ContractSubTask> getFuyouTerminalSubTask(@Param("merchant_sn") String merchantSn);

    @Select("select * from contract_sub_task where merchant_sn=#{merchant_sn} and priority>=#{start} and priority < now() and status !=6 and status_influ_p_task=0 and schedule_status=1 and task_type = 5 and channel = 'fuyou' limit 1")
    ContractSubTask getFuyouUnionTaskWithMerchantSn(@Param("merchant_sn") String merchantSn, @Param("start") String start);


    @DaoLog
    int deleteByPtaskId(Long id);

}