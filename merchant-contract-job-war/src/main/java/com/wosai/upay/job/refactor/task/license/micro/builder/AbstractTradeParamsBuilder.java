package com.wosai.upay.job.refactor.task.license.micro.builder;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.shouqianba.cua.enums.core.PaywayEnum;
import com.shouqianba.cua.utils.object.BeanCopyUtils;
import com.wosai.upay.job.model.MerchantAcquireInfoBO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleMainTaskDO;
import com.wosai.upay.job.refactor.model.entity.InternalScheduleSubTaskDO;
import com.wosai.upay.job.refactor.model.entity.MerchantProviderParamsDO;
import com.wosai.upay.job.refactor.task.license.BusinessLicenceCertificationV3Task;
import com.wosai.upay.job.refactor.task.license.entity.BusinessLicenseCertificationV3MainTaskContext;
import com.wosai.upay.job.refactor.task.license.micro.builder.context.TradeParamsBuilderContext;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessor;
import com.wosai.upay.job.refactor.task.license.micro.builder.processor.PaywayProcessorFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 交易参数构建器抽象基类
 * 使用模板方法模式，定义构建交易参数的通用流程
 * 
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractTradeParamsBuilder {

    /**
     * 构建新的交易参数并返回旧新参数映射关系
     * 模板方法，定义了构建流程
     */
    public Map<String, MerchantProviderParamsDO> buildNewNeedInsertParamsAndReturnOldNewMapV3(
            InternalScheduleMainTaskDO mainTaskDO, InternalScheduleSubTaskDO subTaskDO) {
        
        // 1. 构建上下文
        TradeParamsBuilderContext context = buildContext(mainTaskDO, subTaskDO);
        
        // 2. 获取收单机构信息
        context.setMerchantAcquireInfo(getMerchantAcquireInfo(context));
        
        // 3. 处理每个旧参数
        Map<String, MerchantProviderParamsDO> oldNewParamsMap = Maps.newHashMap();
        
        for (MerchantProviderParamsDO oldParam : context.getOldParams()) {
            // 创建新参数基础信息
            MerchantProviderParamsDO newParam = createNewParamBase(oldParam);
            
            // 根据支付方式处理参数
            PaywayProcessor processor = PaywayProcessorFactory.getProcessor(
                    oldParam.getPayway(), getAcquirerType());
            
            if (processor != null) {
                // 检查是否已存在相同的参数
                if (isDuplicateParam(oldNewParamsMap, oldParam, context)) {
                    continue;
                }
                
                // 处理特定支付方式的参数
                processor.processParam(newParam, oldParam, context);
            }
            
            oldNewParamsMap.put(oldParam.getId(), newParam);
        }
        
        return oldNewParamsMap;
    }

    /**
     * 构建上下文信息
     */
    protected TradeParamsBuilderContext buildContext(InternalScheduleMainTaskDO mainTaskDO, 
                                                   InternalScheduleSubTaskDO subTaskDO) {
        TradeParamsBuilderContext context = new TradeParamsBuilderContext();
        
        // 解析主任务上下文
        BusinessLicenseCertificationV3MainTaskContext mainCtx =
                JSON.parseObject(mainTaskDO.getContext(), BusinessLicenseCertificationV3MainTaskContext.class);
        context.setMainTaskContext(mainCtx);
        context.setMerchant(mainCtx.getMerchant());
        context.setWxAuthTime(mainCtx.getWxAuthTime());
        context.setAliAuthTime(mainCtx.getAliAuthTime());
        
        // 解析子任务上下文
        BusinessLicenceCertificationV3Task.SubTaskContextBOInner subTaskContext =
                JSON.parseObject(subTaskDO.getContext(), BusinessLicenceCertificationV3Task.SubTaskContextBOInner.class);
        context.setSubTaskContext(subTaskContext);
        context.setContractTaskId(subTaskContext.getContractTaskId());
        
        // 获取旧参数列表
        context.setOldParams(subTaskContext.getOldPayWayParamsMap().values().stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toList()));
        
        return context;
    }

    /**
     * 创建新参数的基础信息
     */
    protected MerchantProviderParamsDO createNewParamBase(MerchantProviderParamsDO oldParam) {
        MerchantProviderParamsDO newParam = BeanCopyUtils.copyProperties(oldParam, MerchantProviderParamsDO.class);
        newParam.setId(UUID.randomUUID().toString());
        newParam.setCtime(System.currentTimeMillis());
        newParam.setMtime(System.currentTimeMillis());
        return newParam;
    }

    /**
     * 检查是否为重复参数
     */
    protected boolean isDuplicateParam(Map<String, MerchantProviderParamsDO> existingParams,
                                     MerchantProviderParamsDO oldParam,
                                     TradeParamsBuilderContext context) {
        
        // 只对特定支付方式检查重复
        if (!shouldCheckDuplicate(oldParam.getPayway())) {
            return false;
        }
        
        String targetMerchantId = getTargetMerchantId(oldParam.getPayway(), context);
        if (targetMerchantId == null) {
            return false;
        }
        
        return existingParams.values().stream()
                .filter(param -> Objects.equals(param.getPayway(), oldParam.getPayway()))
                .anyMatch(param -> Objects.equals(param.getPayMerchantId(), targetMerchantId));
    }

    /**
     * 判断是否需要检查重复
     */
    protected boolean shouldCheckDuplicate(Integer payway) {
        return Objects.equals(payway, PaywayEnum.ALIPAY.getValue()) ||
               Objects.equals(payway, PaywayEnum.WEIXIN.getValue()) ||
               Objects.equals(payway, PaywayEnum.UNIONPAY.getValue());
    }

    /**
     * 获取目标商户ID用于重复检查
     */
    protected String getTargetMerchantId(Integer payway, TradeParamsBuilderContext context) {
        if (Objects.equals(payway, PaywayEnum.ALIPAY.getValue())) {
            return context.getMerchantAcquireInfo().getAliNo();
        } else if (Objects.equals(payway, PaywayEnum.WEIXIN.getValue())) {
            return context.getMerchantAcquireInfo().getWxNo();
        } else if (Objects.equals(payway, PaywayEnum.UNIONPAY.getValue())) {
            return context.getMerchantAcquireInfo().getUnionNo();
        }
        return null;
    }

    // ========== 抽象方法，由子类实现 ==========

    /**
     * 获取收单机构类型
     */
    protected abstract String getAcquirerType();

    /**
     * 获取商户收单机构信息
     */
    protected abstract MerchantAcquireInfoBO getMerchantAcquireInfo(TradeParamsBuilderContext context);
}
